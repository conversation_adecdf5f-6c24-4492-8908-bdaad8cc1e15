export interface LineWebhookEvent {
    type: string;
    timestamp: number;
    source: LineEventSource;
    replyToken?: string;
}
export interface LineEventSource {
    type: 'user' | 'group' | 'room';
    userId?: string | undefined;
    groupId?: string | undefined;
    roomId?: string | undefined;
}
export interface LineMessageEvent extends LineWebhookEvent {
    type: 'message';
    message: LineMessageContent;
}
export interface LineMessageContent {
    id: string;
    type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'location' | 'sticker';
    text?: string;
}
export interface LinePostbackEvent extends LineWebhookEvent {
    type: 'postback';
    postback: {
        data: string;
        params?: Record<string, string>;
    };
}
export type LineWebhookEventUnion = LineMessageEvent | LinePostbackEvent | LineWebhookEvent;
//# sourceMappingURL=webhook.d.ts.map