/**
 * SMS message types and interfaces
 */

import { BaseEntity, BaseFilter, ProcessState } from '../base/common';

// Core SMS message interface
export interface SMSMessage {
  id?: string | null;
  message: string;
  ttl?: number | null;
  phoneNumbers: string[];
  simNumber?: number | null;
  withDeliveryReport?: boolean | null;
}

// Recipient state
export interface RecipientState {
  phoneNumber: string;
  state: ProcessState;
  error?: string | null;
}

// Message state
export interface MessageState {
  id: string;
  state: ProcessState;
  recipients: RecipientState[];
}

// Stored message (database model)
export interface StoredMessage extends BaseEntity {
  phoneNumbers: string;
  message: string;
  status: ProcessState;
  deliveredAt?: Date;
  failedReason?: string;
  lineUserId?: string;
  ttl?: number;
  simNumber?: number;
  withDeliveryReport?: boolean;
}

// Message filter
export interface MessageFilter extends BaseFilter {
  status?: ProcessState;
  phoneNumber?: string;
  lineUserId?: string;
}

// Message model operations
export interface CreateMessageData {
  phoneNumbers: string[];
  message: string;
  lineUserId?: string;
  ttl?: number;
  simNumber?: number;
  withDeliveryReport?: boolean;
}

export interface UpdateMessageData {
  status?: ProcessState;
  deliveredAt?: Date;
  failedReason?: string;
}

// API request/response types
export interface SendSMSRequest {
  phoneNumbers: string[];
  message: string;
  ttl?: number;
  simNumber?: number;
  withDeliveryReport?: boolean;
  skipPhoneValidation?: boolean;
}

export interface SendSMSResponse {
  messageId: string;
  status: ProcessState;
  recipients: RecipientState[];
}

export interface MessageStatusRequest {
  messageId: string;
}

export interface GetMessagesRequest {
  page?: number;
  limit?: number;
  status?: ProcessState;
  phoneNumber?: string;
  dateFrom?: string;
  dateTo?: string;
}
