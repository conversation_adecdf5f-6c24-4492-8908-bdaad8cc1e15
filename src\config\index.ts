import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config();

interface Config {
  server: {
    port: number;
    nodeEnv: string;
    baseUrl: string;
    corsOrigin: string;
  };
  database: {
    path: string;
  };
  smsGateway: {
    login: string;
    password: string;
    baseUrl: string;
  };
  lineBot: {
    channelAccessToken: string;
    channelSecret: string;
    webhookPath: string;
    adminUserId?: string;
    quickReplyEnabled: boolean;
    richMenuEnabled: boolean;
  };
  webhooks: {
    smsPath: string;
    smsSecret: string;
  };
  logging: {
    level: string;
    filePath: string;
  };
  security: {
    jwtSecret: string;
    rateLimitWindowMs: number;
    rateLimitMaxRequests: number;
  };
  messaging: {
    defaultTtl: number;
    maxPhoneNumbersPerRequest: number;
    statusCheckInterval: number;
  };
}

const config: Config = {
  server: {
    port: parseInt(process.env.PORT || '3000', 10),
    nodeEnv: process.env.NODE_ENV || 'development',
    baseUrl: process.env.APP_BASE_URL || 'http://localhost:3000',
    corsOrigin: process.env.CORS_ORIGIN || '*',
  },
  database: {
    path: process.env.DATABASE_PATH || './data/sms-gateway.db',
  },
  smsGateway: {
    login: process.env.SMS_GATEWAY_LOGIN || '',
    password: process.env.SMS_GATEWAY_PASSWORD || '',
    baseUrl: process.env.SMS_GATEWAY_BASE_URL || 'https://api.sms-gate.app/3rdparty/v1',
  },
  lineBot: {
    channelAccessToken: process.env.LINE_CHANNEL_ACCESS_TOKEN || '',
    channelSecret: process.env.LINE_CHANNEL_SECRET || '',
    webhookPath: process.env.LINE_WEBHOOK_PATH || '/webhook/line',
    adminUserId: process.env.LINE_ADMIN_USER_ID,
    quickReplyEnabled: process.env.LINE_QUICK_REPLY_ENABLED === 'true',
    richMenuEnabled: process.env.LINE_RICH_MENU_ENABLED === 'true',
  },
  webhooks: {
    smsPath: process.env.SMS_WEBHOOK_PATH || '/webhook/sms',
    smsSecret: process.env.SMS_WEBHOOK_SECRET || '',
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/app.log',
  },
  security: {
    jwtSecret: process.env.JWT_SECRET || 'default-secret-change-in-production',
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },
  messaging: {
    defaultTtl: parseInt(process.env.DEFAULT_MESSAGE_TTL || '3600', 10),
    maxPhoneNumbersPerRequest: parseInt(process.env.MAX_PHONE_NUMBERS_PER_REQUEST || '10', 10),
    statusCheckInterval: parseInt(process.env.MESSAGE_STATUS_CHECK_INTERVAL || '30000', 10),
  },
};

// Validation function
export const validateConfig = (): void => {
  const requiredFields = [
    'SMS_GATEWAY_LOGIN',
    'SMS_GATEWAY_PASSWORD',
    'LINE_CHANNEL_ACCESS_TOKEN',
    'LINE_CHANNEL_SECRET',
  ];

  const missingFields = requiredFields.filter(field => !process.env[field]);
  
  if (missingFields.length > 0) {
    throw new Error(`Missing required environment variables: ${missingFields.join(', ')}`);
  }
};

export default config;
