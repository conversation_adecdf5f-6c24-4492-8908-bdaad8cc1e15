import smsService from '../services/smsService';
import messageModel from '../models/messageModel';
import smsGatewayClient from '../services/smsGatewayClient';
import { ProcessState } from '../types';

// Mock dependencies
jest.mock('../models/messageModel');
jest.mock('../services/smsGatewayClient');
jest.mock('../utils/logger');

const mockMessageModel = messageModel as jest.Mocked<typeof messageModel>;
const mockSmsGatewayClient = smsGatewayClient as jest.Mocked<typeof smsGatewayClient>;

describe('SMSService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('sendMessage', () => {
    it('should send SMS message successfully', async () => {
      // Arrange
      const request = {
        phoneNumbers: ['+1234567890'],
        message: 'Test message',
        withDeliveryReport: true,
      };

      const storedMessage = {
        id: 'test-message-id',
        phoneNumbers: JSON.stringify(request.phoneNumbers),
        message: request.message,
        status: ProcessState.Pending,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const messageState = {
        id: 'test-message-id',
        state: ProcessState.Sent,
        recipients: [
          {
            phoneNumber: '+1234567890',
            state: ProcessState.Sent,
          },
        ],
      };

      mockMessageModel.create.mockResolvedValue(storedMessage);
      mockSmsGatewayClient.sendMessage.mockResolvedValue(messageState);
      mockMessageModel.update.mockResolvedValue();
      mockMessageModel.updateRecipientStatus.mockResolvedValue();

      // Act
      const result = await smsService.sendMessage(request);

      // Assert
      expect(result).toEqual({
        messageId: 'test-message-id',
        status: ProcessState.Sent,
        recipients: messageState.recipients,
      });

      expect(mockMessageModel.create).toHaveBeenCalledWith({
        phoneNumbers: request.phoneNumbers,
        message: request.message,
        lineUserId: undefined,
        ttl: undefined,
        simNumber: undefined,
        withDeliveryReport: true,
      });

      expect(mockSmsGatewayClient.sendMessage).toHaveBeenCalledWith(
        {
          id: 'test-message-id',
          message: request.message,
          phoneNumbers: request.phoneNumbers,
          ttl: undefined,
          simNumber: undefined,
          withDeliveryReport: true,
        },
        { skipPhoneValidation: undefined }
      );

      expect(mockMessageModel.update).toHaveBeenCalledWith('test-message-id', {
        status: ProcessState.Sent,
      });

      expect(mockMessageModel.updateRecipientStatus).toHaveBeenCalledWith(
        'test-message-id',
        '+1234567890',
        ProcessState.Sent,
        undefined
      );
    });

    it('should handle SMS sending failure', async () => {
      // Arrange
      const request = {
        phoneNumbers: ['+1234567890'],
        message: 'Test message',
      };

      const storedMessage = {
        id: 'test-message-id',
        phoneNumbers: JSON.stringify(request.phoneNumbers),
        message: request.message,
        status: ProcessState.Pending,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockMessageModel.create.mockResolvedValue(storedMessage);
      mockSmsGatewayClient.sendMessage.mockRejectedValue(new Error('SMS Gateway error'));

      // Act & Assert
      await expect(smsService.sendMessage(request)).rejects.toThrow('Failed to send SMS: SMS Gateway error');
    });
  });

  describe('getMessageStatus', () => {
    it('should get message status successfully', async () => {
      // Arrange
      const messageId = 'test-message-id';
      const storedMessage = {
        id: messageId,
        phoneNumbers: JSON.stringify(['+1234567890']),
        message: 'Test message',
        status: ProcessState.Sent,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const messageState = {
        id: messageId,
        state: ProcessState.Delivered,
        recipients: [
          {
            phoneNumber: '+1234567890',
            state: ProcessState.Delivered,
          },
        ],
      };

      mockMessageModel.findById.mockResolvedValue(storedMessage);
      mockSmsGatewayClient.getMessageState.mockResolvedValue(messageState);
      mockMessageModel.update.mockResolvedValue();
      mockMessageModel.updateRecipientStatus.mockResolvedValue();

      // Act
      const result = await smsService.getMessageStatus(messageId);

      // Assert
      expect(result).toEqual(messageState);

      expect(mockMessageModel.update).toHaveBeenCalledWith(messageId, {
        status: ProcessState.Delivered,
        deliveredAt: expect.any(Date),
      });

      expect(mockMessageModel.updateRecipientStatus).toHaveBeenCalledWith(
        messageId,
        '+1234567890',
        ProcessState.Delivered,
        undefined
      );
    });

    it('should throw error when message not found', async () => {
      // Arrange
      const messageId = 'non-existent-id';
      mockMessageModel.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(smsService.getMessageStatus(messageId)).rejects.toThrow('Message not found');
    });
  });

  describe('getMessages', () => {
    it('should get messages with pagination', async () => {
      // Arrange
      const filter = { status: ProcessState.Delivered };
      const page = 1;
      const limit = 20;

      const messages = [
        {
          id: 'message-1',
          phoneNumbers: JSON.stringify(['+1234567890']),
          message: 'Test message 1',
          status: ProcessState.Delivered,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'message-2',
          phoneNumbers: JSON.stringify(['+0987654321']),
          message: 'Test message 2',
          status: ProcessState.Delivered,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockMessageModel.findMany.mockResolvedValue({
        messages,
        total: 2,
      });

      // Act
      const result = await smsService.getMessages(filter, page, limit);

      // Assert
      expect(result).toEqual({
        success: true,
        data: messages,
        pagination: {
          page: 1,
          limit: 20,
          total: 2,
          totalPages: 1,
        },
      });

      expect(mockMessageModel.findMany).toHaveBeenCalledWith(filter, { page, limit });
    });
  });

  describe('getMessageDetails', () => {
    it('should get message details with recipients', async () => {
      // Arrange
      const messageId = 'test-message-id';
      const message = {
        id: messageId,
        phoneNumbers: JSON.stringify(['+1234567890']),
        message: 'Test message',
        status: ProcessState.Delivered,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const recipients = [
        {
          phoneNumber: '+1234567890',
          state: ProcessState.Delivered,
        },
      ];

      mockMessageModel.findById.mockResolvedValue(message);
      mockMessageModel.getRecipients.mockResolvedValue(recipients);

      // Act
      const result = await smsService.getMessageDetails(messageId);

      // Assert
      expect(result).toEqual({
        message,
        recipients,
      });

      expect(mockMessageModel.findById).toHaveBeenCalledWith(messageId);
      expect(mockMessageModel.getRecipients).toHaveBeenCalledWith(messageId);
    });

    it('should throw error when message not found', async () => {
      // Arrange
      const messageId = 'non-existent-id';
      mockMessageModel.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(smsService.getMessageDetails(messageId)).rejects.toThrow('Message not found');
    });
  });
});
