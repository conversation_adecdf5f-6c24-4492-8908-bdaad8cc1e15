/**
 * Type adapters for @line/bot-sdk library compatibility
 */

import * as LineSDK from '@line/bot-sdk';
import { LineWebhookEvent, LineMessageEvent, LinePostbackEvent } from '../line/webhook';

// Type adapters to bridge LINE SDK types with our local types
export type LibraryWebhookEvent = LineSDK.WebhookEvent;
export type LibraryMessageEvent = LineSDK.MessageEvent;
export type LibraryTextMessage = LineSDK.TextMessage;
export type LibraryLineMessage = LineSDK.Message;

// Conversion functions
export namespace LineAdapters {
  // Convert LINE SDK WebhookEvent to our LineWebhookEvent
  export function fromLibraryWebhookEvent(event: LibraryWebhookEvent): LineWebhookEvent {
    return {
      type: event.type,
      timestamp: event.timestamp,
      source: {
        type: event.source.type as any,
        userId: event.source.userId,
        groupId: (event.source as any).groupId,
        roomId: (event.source as any).roomId,
      },
      replyToken: (event as any).replyToken,
    };
  }

  // Convert LINE SDK MessageEvent to our LineMessageEvent
  export function fromLibraryMessageEvent(event: LibraryMessageEvent): LineMessageEvent {
    return {
      type: 'message',
      timestamp: event.timestamp,
      source: {
        type: event.source.type as any,
        userId: event.source.userId,
        groupId: (event.source as any).groupId,
        roomId: (event.source as any).roomId,
      },
      replyToken: event.replyToken,
      message: {
        id: event.message.id,
        type: event.message.type as any,
        text: (event.message as any).text,
      },
    };
  }

  // Convert our message to LINE SDK message
  export function toLibraryTextMessage(text: string): LibraryTextMessage {
    return {
      type: 'text',
      text,
    };
  }

  // Type guards for LINE SDK events
  export function isMessageEvent(event: LibraryWebhookEvent): event is LibraryMessageEvent {
    return event.type === 'message';
  }

  export function isTextMessage(message: any): message is { text: string } {
    return message.type === 'text' && typeof message.text === 'string';
  }

  export function isPostbackEvent(event: LibraryWebhookEvent): event is LineSDK.PostbackEvent {
    return event.type === 'postback';
  }
}
