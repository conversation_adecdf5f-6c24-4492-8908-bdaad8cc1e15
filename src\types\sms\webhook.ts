/**
 * SMS webhook types and interfaces
 */

import { BaseEntity } from '../base/common';

// Webhook event types
export enum WebHookEventType {
  SmsReceived = 'sms:received',
  SmsSent = 'sms:sent',
  SmsDelivered = 'sms:delivered',
  SmsFailed = 'sms:failed',
  SystemPing = 'system:ping',
}

// Webhook configuration
export interface WebHook extends BaseEntity {
  event: WebHookEventType;
  url: string;
  deviceId: string;
  active?: boolean;
}

// Webhook registration request
export interface RegisterWebHookRequest {
  id?: string | null;
  event: WebHookEventType;
  url: string;
  deviceId?: string | null;
}

// Webhook payload types
export interface SmsReceivedPayload {
  message: string;
  phoneNumber: string;
  receivedAt: string;
}

export interface SmsSentPayload {
  messageId: string;
  sentAt: string;
}

export interface SmsDeliveredPayload {
  messageId: string;
  deliveredAt: string;
}

export interface SmsFailedPayload {
  messageId: string;
  failedAt: string;
  error: string;
}

export interface SystemPingPayload {
  // Empty object for ping events
}

// Union type for all webhook payloads
export type WebHookPayload = 
  | { event: WebHookEventType.SmsReceived; payload: SmsReceivedPayload }
  | { event: WebHookEventType.SmsSent; payload: SmsSentPayload }
  | { event: WebHookEventType.SmsDelivered; payload: SmsDeliveredPayload }
  | { event: WebHookEventType.SmsFailed; payload: SmsFailedPayload }
  | { event: WebHookEventType.SystemPing; payload: SystemPingPayload };

// Webhook log entry
export interface WebhookLog extends BaseEntity {
  type: 'sms' | 'line';
  payload: string;
  processed: boolean;
  processedAt?: Date;
  error?: string;
}
