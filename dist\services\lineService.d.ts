import { WebhookEvent } from '@line/bot-sdk';
declare class LineService {
    private client;
    constructor();
    handleWebhookEvents(events: WebhookEvent[]): Promise<void>;
    private handleEvent;
    private handleUserEvent;
    private handleMessageEvent;
    private handlePostbackEvent;
    private sendWelcomeMessage;
    private sendMainMenu;
    private handleSendSMSCommand;
    private handleStatusCommand;
    private handleSendSMSAction;
    private handleViewMessagesAction;
    private handleSettingsAction;
    replyMessage(replyToken: string, message: any): Promise<void>;
    pushMessage(userId: string, message: any): Promise<void>;
    broadcastMessage(message: any): Promise<void>;
    sendToAdmins(message: any): Promise<void>;
}
declare const _default: LineService;
export default _default;
//# sourceMappingURL=lineService.d.ts.map