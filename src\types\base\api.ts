/**
 * Base API types for request/response handling
 */

import { PaginationMeta } from './common';

// Generic API response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Paginated API response
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: PaginationMeta;
}

// HTTP Client interface
export interface HttpClient {
  get<T>(url: string, headers?: Record<string, string>): Promise<T>;
  post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  patch<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  delete<T>(url: string, headers?: Record<string, string>): Promise<T>;
}

// Generic request/response types
export interface BaseRequest {
  [key: string]: any;
}

export interface BaseResponse {
  [key: string]: any;
}

// Error response types
export interface ErrorResponse {
  error: string;
  message?: string;
  details?: any;
}

// Health check types
export enum HealthStatus {
  Pass = 'pass',
  Warn = 'warn',
  Fail = 'fail'
}

export interface HealthCheck {
  status: HealthStatus;
  description: string;
  observedValue: number;
  observedUnit: string;
}

export interface HealthResponse {
  status: HealthStatus;
  version: string;
  releaseId: number;
  checks: Record<string, HealthCheck>;
}
