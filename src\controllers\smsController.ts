import { Request, Response } from 'express';
import smsService from '../services/smsService';
import { SendSMSRequest, MessageFilter, ProcessState } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../utils/logger';

class SMSController {
  /**
   * Send SMS message
   * POST /api/sms/send
   */
  sendMessage = asyncHandler(async (req: Request, res: Response) => {
    const request: SendSMSRequest = req.body;
    const lineUserId = req.headers['x-line-user-id'] as string;

    logger.info('SMS send request received', {
      phoneNumbers: request.phoneNumbers,
      messageLength: request.message.length,
      lineUserId,
    });

    const result = await smsService.sendMessage(request, lineUserId);

    res.status(201).json({
      success: true,
      data: result,
      message: 'SMS message sent successfully',
    });
  });

  /**
   * Get message status
   * GET /api/sms/status/:messageId
   */
  getMessageStatus = asyncHandler(async (req: Request, res: Response) => {
    const { messageId } = req.params;

    logger.debug('Message status request received', { messageId });

    const result = await smsService.getMessageStatus(messageId);

    res.json({
      success: true,
      data: result,
    });
  });

  /**
   * Get messages with filtering and pagination
   * GET /api/sms/messages
   */
  getMessages = asyncHandler(async (req: Request, res: Response) => {
    const {
      page = '1',
      limit = '20',
      status,
      phoneNumber,
      lineUserId,
      dateFrom,
      dateTo,
    } = req.query;

    const filter: MessageFilter = {};

    if (status && typeof status === 'string') {
      filter.status = status as ProcessState;
    }

    if (phoneNumber && typeof phoneNumber === 'string') {
      filter.phoneNumber = phoneNumber;
    }

    if (lineUserId && typeof lineUserId === 'string') {
      filter.lineUserId = lineUserId;
    }

    if (dateFrom && typeof dateFrom === 'string') {
      filter.dateFrom = new Date(dateFrom);
    }

    if (dateTo && typeof dateTo === 'string') {
      filter.dateTo = new Date(dateTo);
    }

    logger.debug('Messages request received', {
      filter,
      page: parseInt(page as string),
      limit: parseInt(limit as string),
    });

    const result = await smsService.getMessages(
      filter,
      parseInt(page as string),
      parseInt(limit as string)
    );

    res.json(result);
  });

  /**
   * Get message details
   * GET /api/sms/messages/:messageId
   */
  getMessageDetails = asyncHandler(async (req: Request, res: Response) => {
    const { messageId } = req.params;

    logger.debug('Message details request received', { messageId });

    const result = await smsService.getMessageDetails(messageId);

    res.json({
      success: true,
      data: result,
    });
  });

  /**
   * Refresh message status
   * POST /api/sms/status/:messageId/refresh
   */
  refreshMessageStatus = asyncHandler(async (req: Request, res: Response) => {
    const { messageId } = req.params;

    logger.info('Message status refresh request received', { messageId });

    const result = await smsService.refreshMessageStatus(messageId);

    res.json({
      success: true,
      data: result,
      message: 'Message status refreshed successfully',
    });
  });

  /**
   * Get message statistics
   * GET /api/sms/stats
   */
  getMessageStats = asyncHandler(async (req: Request, res: Response) => {
    const { dateFrom, dateTo } = req.query;

    const dateFromObj = dateFrom && typeof dateFrom === 'string' 
      ? new Date(dateFrom) 
      : undefined;
    
    const dateToObj = dateTo && typeof dateTo === 'string' 
      ? new Date(dateTo) 
      : undefined;

    logger.debug('Message stats request received', {
      dateFrom: dateFromObj,
      dateTo: dateToObj,
    });

    const result = await smsService.getMessageStats(dateFromObj, dateToObj);

    res.json({
      success: true,
      data: result,
    });
  });

  /**
   * Health check for SMS service
   * GET /api/sms/health
   */
  healthCheck = asyncHandler(async (req: Request, res: Response) => {
    logger.debug('SMS health check request received');

    // Test SMS Gateway connection
    const isConnected = await smsService.getMessageStats();

    res.json({
      success: true,
      data: {
        smsGateway: 'connected',
        timestamp: new Date().toISOString(),
      },
      message: 'SMS service is healthy',
    });
  });
}

export default new SMSController();
