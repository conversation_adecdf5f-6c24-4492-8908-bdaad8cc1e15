export declare enum LimitPeriod {
    Disabled = "Disabled",
    PerMinute = "PerMinute",
    PerHour = "PerHour",
    PerDay = "PerDay"
}
export declare enum SimSelectionMode {
    OSDefault = "OSDefault",
    RoundRobin = "RoundRobin",
    Random = "Random"
}
export interface SettingsMessages {
    limitPeriod?: LimitPeriod | undefined;
    limitValue?: number | undefined;
    logLifetimeDays?: number | undefined;
    sendIntervalMin?: number | undefined;
    sendIntervalMax?: number | undefined;
    simSelectionMode?: SimSelectionMode | undefined;
}
export interface SettingsWebhooks {
    internetRequired?: boolean | undefined;
    retryCount?: number | undefined;
    signingKey?: string | undefined;
}
export interface SettingsGateway {
    name?: string | undefined;
    cloudUrl?: string | undefined;
    privateToken?: string | undefined;
}
export interface SettingsEncryption {
    enabled?: boolean | undefined;
    passphrase?: string | undefined;
}
export interface SettingsLogs {
    ttl?: number | undefined;
    lifetimeDays?: number | undefined;
}
export interface SettingsPing {
    enabled?: boolean | undefined;
    interval?: number | undefined;
    intervalSeconds?: number | undefined;
}
export interface DeviceSettings {
    messages?: SettingsMessages | undefined;
    webhooks?: SettingsWebhooks | undefined;
    gateway?: SettingsGateway | undefined;
    encryption?: SettingsEncryption | undefined;
    logs?: SettingsLogs | undefined;
    ping?: SettingsPing | undefined;
}
export interface UpdateSettingsRequest extends DeviceSettings {
}
export interface PatchSettingsRequest extends Partial<DeviceSettings> {
}
//# sourceMappingURL=settings.d.ts.map