import { Request, Response } from 'express';
import webhookService from '../services/webhookService';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../utils/logger';

class WebhookController {
  /**
   * Handle SMS Gateway webhook
   * POST /webhook/sms
   */
  handleSMSWebhook = asyncHandler(async (req: Request, res: Response) => {
    const payload = req.body;

    logger.info('SMS webhook received', {
      event: payload.event,
      messageId: payload.messageId,
      deviceId: payload.deviceId,
      phoneNumber: payload.phoneNumber,
    });

    try {
      await webhookService.handleSMSWebhook(payload);

      res.status(200).json({
        success: true,
        message: 'SMS webhook processed successfully',
      });
    } catch (error) {
      logger.error('Failed to process SMS webhook', {
        error: (error as Error).message,
        payload,
      });

      // Return 200 to prevent SMS Gateway from retrying
      res.status(200).json({
        success: false,
        error: 'Failed to process webhook',
      });
    }
  });

  /**
   * Get registered webhooks
   * GET /api/webhooks
   */
  getWebhooks = asyncHandler(async (_req: Request, res: Response) => {
    logger.debug('Get webhooks request received');

    const webhooks = await webhookService.getWebhooks();

    res.json({
      success: true,
      data: webhooks,
    });
  });

  /**
   * Register SMS webhook
   * POST /api/webhooks/register
   */
  registerWebhook = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Register webhook request received');

    const webhook = await webhookService.registerSMSWebhook();

    res.status(201).json({
      success: true,
      data: webhook,
      message: 'Webhook registered successfully',
    });
  });

  /**
   * Delete webhook
   * DELETE /api/webhooks/:webhookId
   */
  deleteWebhook = asyncHandler(async (req: Request, res: Response) => {
    const { webhookId } = req.params;

    logger.info('Delete webhook request received', { webhookId });

    await webhookService.deleteWebhook(webhookId);

    res.json({
      success: true,
      message: 'Webhook deleted successfully',
    });
  });

  /**
   * Test webhook endpoint
   * POST /webhook/sms/test
   */
  testWebhook = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Test webhook request received', { body: req.body });

    res.json({
      success: true,
      message: 'Test webhook received successfully',
      timestamp: new Date().toISOString(),
      payload: req.body,
    });
  });

  /**
   * Webhook health check
   * GET /webhook/health
   */
  healthCheck = asyncHandler(async (req: Request, res: Response) => {
    logger.debug('Webhook health check request received');

    res.json({
      success: true,
      data: {
        webhooks: 'active',
        smsWebhookPath: '/webhook/sms',
        lineWebhookPath: '/webhook/line',
        timestamp: new Date().toISOString(),
      },
      message: 'Webhook service is healthy',
    });
  });
}

export default new WebhookController();
