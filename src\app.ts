import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import swaggerUi from 'swagger-ui-express';
import config from './config';
import swaggerSpecs from './config/swagger';
import logger from './utils/logger';
import RateLimiter from './middleware/rateLimiter';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';

// Import routes
import smsRoutes from './routes/sms';
import lineRoutes from './routes/line';
import webhookRoutes from './routes/webhooks';
import deviceRoutes from './routes/devices';

class App {
  public app: express.Application;
  private rateLimiter: RateLimiter;

  constructor() {
    this.app = express();
    this.rateLimiter = new RateLimiter(
      config.security.rateLimitWindowMs,
      config.security.rateLimitMaxRequests
    );

    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddlewares(): void {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: config.server.corsOrigin,
      credentials: true,
    }));

    // Rate limiting
    this.app.use(this.rateLimiter.middleware());

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging middleware
    this.app.use(morgan('combined', {
      stream: {
        write: (message: string) => {
          logger.info(message.trim());
        },
      },
    }));

    // Request logging
    this.app.use((req, _res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: req.method !== 'GET' ? req.body : undefined,
      });
      next();
    });
  }

  private initializeRoutes(): void {
    // Swagger documentation
    this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: 'SMS Gateway API Documentation',
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        docExpansion: 'none',
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
      },
    }));

    // Health check endpoint
    this.app.get('/health', (_req, res) => {
      res.json({
        success: true,
        message: 'SMS Gateway API is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      });
    });

    // API routes
    this.app.use('/api/sms', smsRoutes);
    this.app.use('/api/devices', deviceRoutes);
    this.app.use('/api/webhooks', webhookRoutes);
    this.app.use('/webhook', webhookRoutes);
    this.app.use(config.lineBot.webhookPath, lineRoutes);
    this.app.use('/api/line', lineRoutes);

    // API documentation endpoint
    this.app.get('/api', (req, res) => {
      res.json({
        success: true,
        message: 'SMS Gateway API Documentation',
        documentation: {
          swagger: `${req.protocol}://${req.get('host')}/api-docs`,
          interactive: 'Visit /api-docs for interactive API documentation',
        },
        endpoints: {
          health: 'GET /health',
          sms: {
            send: 'POST /api/sms/send',
            status: 'GET /api/sms/status/:messageId',
            messages: 'GET /api/sms/messages',
          },
          devices: {
            list: 'GET /api/devices',
            settings: 'GET /api/devices/settings',
            updateSettings: 'PUT /api/devices/settings',
          },
          webhooks: {
            sms: `POST ${config.webhooks.smsPath}`,
            line: `POST ${config.lineBot.webhookPath}`,
          },
        },
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  public listen(): void {
    this.app.listen(config.server.port, () => {
      logger.info(`Server is running on port ${config.server.port}`, {
        environment: config.server.nodeEnv,
        baseUrl: config.server.baseUrl,
      });
    });
  }
}

export default App;
