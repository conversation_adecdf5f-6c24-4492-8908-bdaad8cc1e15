import smsGatewayClient from './smsGatewayClient';
import messageModel from '../models/messageModel';
import {
  SendSMSRequest,
  SendSMSResponse,
  MessageState,
  ProcessState,
  StoredMessage,
  MessageFilter,
  PaginatedResponse,
  SMSMessage,
} from '../types';
import logger from '../utils/logger';
import { AppError } from '../middleware/errorHandler';

class SMSService {
  /**
   * Send SMS message
   */
  async sendMessage(
    request: SendSMSRequest,
    lineUserId?: string
  ): Promise<SendSMSResponse> {
    try {
      logger.info('Sending SMS message', {
        phoneNumbers: request.phoneNumbers,
        messageLength: request.message.length,
        lineUserId,
      });

      // Store message in database first
      const storedMessage = await messageModel.create({
        phoneNumbers: request.phoneNumbers,
        message: request.message,
        lineUserId,
        ttl: request.ttl,
        simNumber: request.simNumber,
        withDeliveryReport: request.withDeliveryReport,
      });

      // Send message via SMS Gateway
      const smsMessage: SMSMessage = {
        id: storedMessage.id,
        message: request.message,
        phoneNumbers: request.phoneNumbers,
        ttl: request.ttl ?? null,
        simNumber: request.simNumber ?? null,
        withDeliveryReport: request.withDeliveryReport ?? null,
      };

      const messageState = await smsGatewayClient.sendMessage(
        smsMessage,
        request.skipPhoneValidation !== undefined
          ? { skipPhoneValidation: request.skipPhoneValidation }
          : {}
      );

      // Update message status in database
      await messageModel.update(storedMessage.id, {
        status: messageState.state,
      });

      // Update recipient statuses
      for (const recipient of messageState.recipients) {
        await messageModel.updateRecipientStatus(
          storedMessage.id,
          recipient.phoneNumber,
          recipient.state,
          recipient.error
        );
      }

      logger.info('SMS message sent successfully', {
        messageId: storedMessage.id,
        status: messageState.state,
        recipientCount: messageState.recipients.length,
      });

      return {
        messageId: storedMessage.id,
        status: messageState.state,
        recipients: messageState.recipients,
      };
    } catch (error) {
      logger.error('Failed to send SMS message', {
        error: (error as Error).message,
        phoneNumbers: request.phoneNumbers,
        lineUserId,
      });

      throw new AppError(
        `Failed to send SMS: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get message status
   */
  async getMessageStatus(messageId: string): Promise<MessageState> {
    try {
      logger.debug('Getting message status', { messageId });

      // Get message from database
      const storedMessage = await messageModel.findById(messageId);
      if (!storedMessage) {
        throw new AppError('Message not found', 404);
      }

      // Get current status from SMS Gateway
      const messageState = await smsGatewayClient.getMessageState(messageId);

      // Update database with latest status
      if (messageState.state !== storedMessage.status) {
        await messageModel.update(messageId, {
          status: messageState.state,
          ...(messageState.state === ProcessState.Delivered && {
            deliveredAt: new Date(),
          }),
        });

        // Update recipient statuses
        for (const recipient of messageState.recipients) {
          await messageModel.updateRecipientStatus(
            messageId,
            recipient.phoneNumber,
            recipient.state,
            recipient.error
          );
        }
      }

      logger.debug('Message status retrieved', {
        messageId,
        status: messageState.state,
        recipientCount: messageState.recipients.length,
      });

      return messageState;
    } catch (error) {
      logger.error('Failed to get message status', {
        error: (error as Error).message,
        messageId,
      });

      if (error instanceof AppError) {
        throw error;
      }

      throw new AppError(
        `Failed to get message status: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get messages with filtering and pagination
   */
  async getMessages(
    filter: MessageFilter = {},
    page: number = 1,
    limit: number = 20
  ): Promise<PaginatedResponse<StoredMessage>> {
    try {
      logger.debug('Getting messages', { filter, page, limit });

      const { messages, total } = await messageModel.findMany(
        filter,
        { page, limit }
      );

      const totalPages = Math.ceil(total / limit);

      logger.debug('Messages retrieved', {
        count: messages.length,
        total,
        page,
        totalPages,
      });

      return {
        success: true,
        data: messages,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Failed to get messages', {
        error: (error as Error).message,
        filter,
        page,
        limit,
      });

      throw new AppError(
        `Failed to get messages: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get message details including recipients
   */
  async getMessageDetails(messageId: string): Promise<{
    message: StoredMessage;
    recipients: any[];
  }> {
    try {
      logger.debug('Getting message details', { messageId });

      const message = await messageModel.findById(messageId);
      if (!message) {
        throw new AppError('Message not found', 404);
      }

      const recipients = await messageModel.getRecipients(messageId);

      logger.debug('Message details retrieved', {
        messageId,
        recipientCount: recipients.length,
      });

      return {
        message,
        recipients,
      };
    } catch (error) {
      logger.error('Failed to get message details', {
        error: (error as Error).message,
        messageId,
      });

      if (error instanceof AppError) {
        throw error;
      }

      throw new AppError(
        `Failed to get message details: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Refresh message status from SMS Gateway
   */
  async refreshMessageStatus(messageId: string): Promise<MessageState> {
    try {
      logger.info('Refreshing message status', { messageId });

      const messageState = await smsGatewayClient.getMessageState(messageId);

      // Update database
      await messageModel.update(messageId, {
        status: messageState.state,
        ...(messageState.state === ProcessState.Delivered && {
          deliveredAt: new Date(),
        }),
        ...(messageState.state === ProcessState.Failed && {
          failedReason: 'Message failed to send',
        }),
      });

      // Update recipient statuses
      for (const recipient of messageState.recipients) {
        await messageModel.updateRecipientStatus(
          messageId,
          recipient.phoneNumber,
          recipient.state,
          recipient.error
        );
      }

      logger.info('Message status refreshed', {
        messageId,
        status: messageState.state,
      });

      return messageState;
    } catch (error) {
      logger.error('Failed to refresh message status', {
        error: (error as Error).message,
        messageId,
      });

      throw new AppError(
        `Failed to refresh message status: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get message statistics
   */
  async getMessageStats(dateFrom?: Date, dateTo?: Date): Promise<{
    total: number;
    pending: number;
    sent: number;
    delivered: number;
    failed: number;
    byDate: Array<{ date: string; count: number }>;
  }> {
    try {
      logger.debug('Getting message statistics', { dateFrom, dateTo });

      const filter: MessageFilter = {};
      if (dateFrom) filter.dateFrom = dateFrom;
      if (dateTo) filter.dateTo = dateTo;

      const { messages } = await messageModel.findMany(filter, { page: 1, limit: 10000 });

      const stats = {
        total: messages.length,
        pending: messages.filter(m => m.status === ProcessState.Pending).length,
        sent: messages.filter(m => m.status === ProcessState.Sent).length,
        delivered: messages.filter(m => m.status === ProcessState.Delivered).length,
        failed: messages.filter(m => m.status === ProcessState.Failed).length,
        byDate: [] as Array<{ date: string; count: number }>,
      };

      // Group by date
      const dateGroups = messages.reduce((acc, message) => {
        const date = message.createdAt.toISOString().split('T')[0]!;
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      stats.byDate = Object.entries(dateGroups)
        .map(([date, count]) => ({ date, count }))
        .sort((a, b) => a.date.localeCompare(b.date));

      logger.debug('Message statistics calculated', stats);

      return stats;
    } catch (error) {
      logger.error('Failed to get message statistics', {
        error: (error as Error).message,
        dateFrom,
        dateTo,
      });

      throw new AppError(
        `Failed to get message statistics: ${(error as Error).message}`,
        500
      );
    }
  }
}

export default new SMSService();
