import { Router } from 'express';
import webhookController from '../controllers/webhookController';
import { validateWebhookSignature } from '../middleware/validation';
import config from '../config';

const router = Router();

/**
 * @route   POST /webhook/sms
 * @desc    Handle SMS Gateway webhook
 * @access  Public (with signature validation)
 */
router.post(
  '/sms',
  validateWebhookSignature(config.webhooks.smsSecret),
  webhookController.handleSMSWebhook
);

/**
 * @route   POST /webhook/sms/test
 * @desc    Test SMS webhook endpoint
 * @access  Public
 */
router.post('/sms/test', webhookController.testWebhook);

/**
 * @route   GET /webhook/health
 * @desc    Webhook service health check
 * @access  Public
 */
router.get('/health', webhookController.healthCheck);

/**
 * @route   GET /api/webhooks
 * @desc    Get registered webhooks
 * @access  Public
 */
router.get('/', webhookController.getWebhooks);

/**
 * @route   POST /api/webhooks/register
 * @desc    Register SMS webhook
 * @access  Public
 */
router.post('/register', webhookController.registerWebhook);

/**
 * @route   DELETE /api/webhooks/:webhookId
 * @desc    Delete webhook
 * @access  Public
 */
router.delete('/:webhookId', webhookController.deleteWebhook);

export default router;
