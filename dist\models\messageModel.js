"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const uuid_1 = require("uuid");
const database_1 = __importDefault(require("./database"));
const types_1 = require("../types");
const logger_1 = __importDefault(require("../utils/logger"));
class MessageModel {
    async create(data) {
        const id = (0, uuid_1.v4)();
        const now = new Date();
        const message = {
            id,
            phoneNumbers: JSON.stringify(data.phoneNumbers),
            message: data.message,
            status: types_1.ProcessState.Pending,
            createdAt: now,
            updatedAt: now,
            lineUserId: data.lineUserId,
        };
        await database_1.default.run(`INSERT INTO messages (
        id, phone_numbers, message, status, created_at, updated_at, 
        line_user_id, ttl, sim_number, with_delivery_report
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
            message.id,
            message.phoneNumbers,
            message.message,
            message.status,
            message.createdAt.toISOString(),
            message.updatedAt.toISOString(),
            message.lineUserId || null,
            data.ttl || null,
            data.simNumber || null,
            data.withDeliveryReport ? 1 : 0,
        ]);
        for (const phoneNumber of data.phoneNumbers) {
            await database_1.default.run(`INSERT INTO message_recipients (
          id, message_id, phone_number, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?)`, [
                (0, uuid_1.v4)(),
                id,
                phoneNumber,
                types_1.ProcessState.Pending,
                now.toISOString(),
                now.toISOString(),
            ]);
        }
        logger_1.default.info('Message created in database', {
            messageId: id,
            phoneNumbers: data.phoneNumbers,
            messageLength: data.message.length,
        });
        return message;
    }
    async findById(id) {
        const row = await database_1.default.get('SELECT * FROM messages WHERE id = ?', [id]);
        if (!row) {
            return null;
        }
        return this.mapRowToMessage(row);
    }
    async update(id, data) {
        const updates = [];
        const values = [];
        if (data.status !== undefined) {
            updates.push('status = ?');
            values.push(data.status);
        }
        if (data.deliveredAt !== undefined) {
            updates.push('delivered_at = ?');
            values.push(data.deliveredAt.toISOString());
        }
        if (data.failedReason !== undefined) {
            updates.push('failed_reason = ?');
            values.push(data.failedReason);
        }
        updates.push('updated_at = ?');
        values.push(new Date().toISOString());
        values.push(id);
        await database_1.default.run(`UPDATE messages SET ${updates.join(', ')} WHERE id = ?`, values);
        logger_1.default.debug('Message updated in database', { messageId: id, updates: data });
    }
    async updateRecipientStatus(messageId, phoneNumber, status, error) {
        const updates = ['status = ?', 'updated_at = ?'];
        const values = [status, new Date().toISOString()];
        if (status === types_1.ProcessState.Delivered) {
            updates.push('delivered_at = ?');
            values.push(new Date().toISOString());
        }
        if (error) {
            updates.push('error = ?');
            values.push(error);
        }
        values.push(messageId, phoneNumber);
        await database_1.default.run(`UPDATE message_recipients SET ${updates.join(', ')} 
       WHERE message_id = ? AND phone_number = ?`, values);
        logger_1.default.debug('Recipient status updated', {
            messageId,
            phoneNumber,
            status,
            error,
        });
    }
    async getRecipients(messageId) {
        const rows = await database_1.default.all('SELECT * FROM message_recipients WHERE message_id = ?', [messageId]);
        return rows.map(row => ({
            phoneNumber: row.phone_number,
            state: row.status,
            error: row.error !== undefined ? row.error : null,
        }));
    }
    async findMany(filter = {}, pagination = { page: 1, limit: 20 }) {
        const conditions = [];
        const values = [];
        if (filter.status) {
            conditions.push('status = ?');
            values.push(filter.status);
        }
        if (filter.lineUserId) {
            conditions.push('line_user_id = ?');
            values.push(filter.lineUserId);
        }
        if (filter.dateFrom) {
            conditions.push('created_at >= ?');
            values.push(filter.dateFrom.toISOString());
        }
        if (filter.dateTo) {
            conditions.push('created_at <= ?');
            values.push(filter.dateTo.toISOString());
        }
        if (filter.phoneNumber) {
            conditions.push('phone_numbers LIKE ?');
            values.push(`%${filter.phoneNumber}%`);
        }
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        const countResult = await database_1.default.get(`SELECT COUNT(*) as count FROM messages ${whereClause}`, values);
        const total = countResult?.count || 0;
        const offset = (pagination.page - 1) * pagination.limit;
        const rows = await database_1.default.all(`SELECT * FROM messages ${whereClause} 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`, [...values, pagination.limit, offset]);
        const messages = rows.map(row => this.mapRowToMessage(row));
        return { messages, total };
    }
    async deleteOldMessages(olderThanDays = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
        const result = await database_1.default.run('DELETE FROM messages WHERE created_at < ?', [cutoffDate.toISOString()]);
        logger_1.default.info('Old messages deleted', {
            deletedCount: result.changes,
            cutoffDate: cutoffDate.toISOString(),
        });
        return result.changes || 0;
    }
    mapRowToMessage(row) {
        return {
            id: row.id,
            phoneNumbers: row.phone_numbers,
            message: row.message,
            status: row.status,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at),
            deliveredAt: row.delivered_at ? new Date(row.delivered_at) : undefined,
            failedReason: row.failed_reason || undefined,
            lineUserId: row.line_user_id || undefined,
        };
    }
}
exports.default = new MessageModel();
//# sourceMappingURL=messageModel.js.map