import { Router } from 'express';
import lineController from '../controllers/lineController';
import { validateLineSignature } from '../middleware/validation';
import config from '../config';

const router = Router();

/**
 * @route   POST /webhook/line
 * @desc    Handle LINE webhook events
 * @access  Public (with signature validation)
 */
router.post(
  '/',
  validateLineSignature(config.lineBot.channelSecret),
  lineController.handleWebhook
);

/**
 * @swagger
 * /api/line/send:
 *   post:
 *     tags: [LINE Bot]
 *     summary: Send message to specific user
 *     description: Send a message to a specific LINE user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LineMessage'
 *           example:
 *             userId: "U503f97b9790a1fab78392736528b53ab"
 *             message:
 *               type: "text"
 *               text: "Hello from API!"
 *     responses:
 *       200:
 *         description: Message sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: Bad request - missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
router.post('/send', lineController.sendMessage);

/**
 * @swagger
 * /api/line/broadcast:
 *   post:
 *     tags: [LINE Bot]
 *     summary: Broadcast message to all users
 *     description: Send a message to all LINE Bot users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [message]
 *             properties:
 *               message:
 *                 type: object
 *                 description: LINE message object
 *                 properties:
 *                   type:
 *                     type: string
 *                     example: "text"
 *                   text:
 *                     type: string
 *                     example: "Broadcast message to all users"
 *           example:
 *             message:
 *               type: "text"
 *               text: "Broadcast message to all users"
 *     responses:
 *       200:
 *         description: Broadcast message sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
router.post('/broadcast', lineController.broadcastMessage);

/**
 * @route   POST /api/line/send-to-admins
 * @desc    Send message to admin users
 * @access  Public
 */
router.post('/send-to-admins', lineController.sendToAdmins);

/**
 * @route   GET /api/line/info
 * @desc    Get LINE Bot information
 * @access  Public
 */
router.get('/info', lineController.getBotInfo);

/**
 * @route   GET /api/line/health
 * @desc    LINE Bot service health check
 * @access  Public
 */
router.get('/health', lineController.healthCheck);

export default router;
