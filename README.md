# SMS Gateway API with LINE Bot Integration

A comprehensive Node.js API application that integrates with the Android SMS Gateway service and LINE Bot for seamless SMS management and real-time notifications.

## Features

- 📱 **SMS Management**: Send, track, and manage SMS messages through Android SMS Gateway
- 🤖 **LINE Bot Integration**: Control SMS operations directly from LINE chat
- 📊 **Real-time Status Tracking**: Monitor message delivery status with webhooks
- 🔧 **Device Management**: Configure and manage SMS gateway devices
- 📋 **Message History**: View and filter SMS message history
- 🔔 **Instant Notifications**: Receive SMS status updates in LINE
- ⚙️ **Settings Management**: Configure device settings and preferences
- 🔒 **Secure Webhooks**: Validated webhook endpoints for reliable communication

## Quick Start

### Prerequisites

- Node.js 18+ 
- Android SMS Gateway app installed on your device
- LINE Bot channel (for LINE integration)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sms-be
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Build and start the application**
   ```bash
   npm run build
   npm start
   ```

   For development:
   ```bash
   npm run dev
   ```

## Configuration

### Environment Variables

Create a `.env` file with the following configuration:

```env
# Server Configuration
PORT=3000
NODE_ENV=development
APP_BASE_URL=http://localhost:3000

# Android SMS Gateway Configuration
SMS_GATEWAY_LOGIN=your_sms_gateway_username
SMS_GATEWAY_PASSWORD=your_sms_gateway_password
SMS_GATEWAY_BASE_URL=https://api.sms-gate.app/3rdparty/v1

# LINE Bot Configuration
LINE_CHANNEL_ACCESS_TOKEN=your_line_channel_access_token
LINE_CHANNEL_SECRET=your_line_channel_secret
LINE_ADMIN_USER_ID=your_line_admin_user_id

# Database Configuration
DATABASE_PATH=./data/sms-gateway.db

# Webhook Configuration
SMS_WEBHOOK_SECRET=your_webhook_secret_key
```

### LINE Bot Setup

1. Create a LINE Bot channel at [LINE Developers Console](https://developers.line.biz/)
2. Get your Channel Access Token and Channel Secret
3. Set webhook URL to: `https://your-domain.com/webhook/line`
4. Enable webhooks in your LINE Bot settings

### Android SMS Gateway Setup

1. Install the SMS Gateway for Android app
2. Create an account and get your login credentials
3. Configure webhook URL to: `https://your-domain.com/webhook/sms`

## API Documentation

### SMS Endpoints

#### Send SMS Message
```http
POST /api/sms/send
Content-Type: application/json

{
  "phoneNumbers": ["+**********"],
  "message": "Hello from SMS Gateway!",
  "withDeliveryReport": true
}
```

#### Get Message Status
```http
GET /api/sms/status/{messageId}
```

#### Get Messages (with filtering)
```http
GET /api/sms/messages?page=1&limit=20&status=Delivered&phoneNumber=+**********
```

#### Get Message Statistics
```http
GET /api/sms/stats?dateFrom=2024-01-01&dateTo=2024-01-31
```

### Device Management Endpoints

#### Get Devices
```http
GET /api/devices
```

#### Get Device Settings
```http
GET /api/devices/settings
```

#### Update Device Settings
```http
PUT /api/devices/settings
Content-Type: application/json

{
  "messages": {
    "limitPeriod": "PerDay",
    "limitValue": 100
  },
  "webhooks": {
    "internetRequired": true,
    "retryCount": 3
  }
}
```

#### Get System Health
```http
GET /api/devices/health
```

### LINE Bot Endpoints

#### Send Message to User
```http
POST /api/line/send
Content-Type: application/json

{
  "userId": "line_user_id",
  "message": {
    "type": "text",
    "text": "Hello from API!"
  }
}
```

#### Broadcast Message
```http
POST /api/line/broadcast
Content-Type: application/json

{
  "message": {
    "type": "text",
    "text": "Broadcast message to all users"
  }
}
```

### Webhook Endpoints

#### SMS Gateway Webhook
```http
POST /webhook/sms
X-Webhook-Signature: your_webhook_signature
Content-Type: application/json

{
  "event": "sms:received",
  "deviceId": "device_id",
  "phoneNumber": "+**********",
  "message": "Incoming SMS message",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### LINE Bot Webhook
```http
POST /webhook/line
X-Line-Signature: line_signature
Content-Type: application/json

{
  "events": [
    {
      "type": "message",
      "message": {
        "type": "text",
        "text": "/send +********** Hello world!"
      },
      "source": {
        "userId": "line_user_id"
      },
      "replyToken": "reply_token"
    }
  ]
}
```

## LINE Bot Commands

### Available Commands

- `/send [phone] [message]` - Send SMS message
- `/status` - Check recent message status
- `/messages` - View recent messages
- `/help` or `/menu` - Show available commands

### Quick Actions

The LINE Bot provides quick action buttons for:
- 📱 Send SMS
- 📊 Check Status  
- 📋 View Messages
- ⚙️ Settings

### Example Usage

```
/send +********** Hello from LINE Bot!
```

This will:
1. Send the SMS through the gateway
2. Store the message in the database
3. Provide immediate confirmation
4. Send delivery updates when available

## Database Schema

The application uses SQLite with the following main tables:

- `messages` - SMS message records
- `message_recipients` - Individual recipient status tracking
- `line_users` - LINE Bot user profiles
- `line_messages` - LINE Bot message history
- `webhook_logs` - Webhook event logs
- `settings` - Application settings

## Development

### Project Structure

```
src/
├── config/          # Configuration management
├── controllers/     # Request handlers
├── middleware/      # Express middleware
├── models/          # Database models
├── routes/          # API routes
├── services/        # Business logic
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
└── __tests__/       # Test files
```

### Running Tests

```bash
npm test
```

### Linting

```bash
npm run lint
npm run lint:fix
```

### Building

```bash
npm run build
```

## Deployment

### Using PM2

```bash
npm install -g pm2
pm2 start dist/index.js --name sms-gateway-api
```

### Using Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

### Environment Setup

For production deployment:

1. Set `NODE_ENV=production`
2. Use HTTPS for webhook URLs
3. Configure proper database backup
4. Set up monitoring and logging
5. Use environment-specific secrets

## Monitoring

### Health Checks

- `/health` - General application health
- `/api/sms/health` - SMS service health
- `/api/line/health` - LINE Bot service health
- `/api/devices/health` - Device gateway health
- `/webhook/health` - Webhook service health

### Logging

The application provides structured logging with different levels:
- `error` - Error conditions
- `warn` - Warning conditions  
- `info` - Informational messages
- `debug` - Debug information

Logs are written to both console and file (configurable).

## Troubleshooting

### Common Issues

1. **SMS Gateway Connection Failed**
   - Check your login credentials
   - Verify the base URL is correct
   - Ensure your device is online

2. **LINE Bot Not Responding**
   - Verify webhook URL is accessible
   - Check LINE channel configuration
   - Validate webhook signature

3. **Database Errors**
   - Ensure database directory exists
   - Check file permissions
   - Verify SQLite installation

4. **Webhook Not Receiving Events**
   - Confirm webhook URLs are publicly accessible
   - Check webhook signatures
   - Verify SSL certificates for HTTPS

### Debug Mode

Enable debug logging by setting:
```env
LOG_LEVEL=debug
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and linting
6. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For support and questions:
- Check the troubleshooting section
- Review the API documentation
- Create an issue in the repository
