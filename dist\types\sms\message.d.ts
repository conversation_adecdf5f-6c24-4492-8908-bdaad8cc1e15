import { BaseEntity, BaseFilter, ProcessState } from '../base/common';
export interface SMSMessage {
    id?: string | null;
    message: string;
    ttl?: number | null;
    phoneNumbers: string[];
    simNumber?: number | null;
    withDeliveryReport?: boolean | null;
}
export interface RecipientState {
    phoneNumber: string;
    state: ProcessState;
    error?: string | null;
}
export interface MessageState {
    id: string;
    state: ProcessState;
    recipients: RecipientState[];
}
export interface StoredMessage extends BaseEntity {
    phoneNumbers: string;
    message: string;
    status: ProcessState;
    deliveredAt?: Date | undefined;
    failedReason?: string | undefined;
    lineUserId?: string | undefined;
    ttl?: number | undefined;
    simNumber?: number | undefined;
    withDeliveryReport?: boolean | undefined;
}
export interface MessageFilter extends BaseFilter {
    status?: ProcessState | undefined;
    phoneNumber?: string | undefined;
    lineUserId?: string | undefined;
}
export interface CreateMessageData {
    phoneNumbers: string[];
    message: string;
    lineUserId?: string | undefined;
    ttl?: number | undefined;
    simNumber?: number | undefined;
    withDeliveryReport?: boolean | undefined;
}
export interface UpdateMessageData {
    status?: ProcessState | undefined;
    deliveredAt?: Date | undefined;
    failedReason?: string | undefined;
}
export interface SendSMSRequest {
    phoneNumbers: string[];
    message: string;
    ttl?: number;
    simNumber?: number;
    withDeliveryReport?: boolean;
    skipPhoneValidation?: boolean;
}
export interface SendSMSResponse {
    messageId: string;
    status: ProcessState;
    recipients: RecipientState[];
}
export interface MessageStatusRequest {
    messageId: string;
}
export interface GetMessagesRequest {
    page?: number;
    limit?: number;
    status?: ProcessState;
    phoneNumber?: string;
    dateFrom?: string;
    dateTo?: string;
}
//# sourceMappingURL=message.d.ts.map