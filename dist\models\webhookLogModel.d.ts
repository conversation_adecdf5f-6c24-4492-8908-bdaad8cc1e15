import { WebhookLog, CreateWebhookLogData, UpdateWebhookLogData, WebhookLogFilter } from '../types';
export interface PaginationOptions {
    page: number;
    limit: number;
}
declare class WebhookLogModel {
    create(data: CreateWebhookLogData): Promise<WebhookLog>;
    findById(id: string): Promise<WebhookLog | null>;
    update(id: string, data: UpdateWebhookLogData): Promise<void>;
    markAsProcessed(id: string, error?: string): Promise<void>;
    findMany(filter?: WebhookLogFilter, pagination?: PaginationOptions): Promise<{
        logs: WebhookLog[];
        total: number;
    }>;
    findUnprocessed(limit?: number): Promise<WebhookLog[]>;
    getStats(): Promise<{
        total: number;
        processed: number;
        unprocessed: number;
        errors: number;
        smsLogs: number;
        lineLogs: number;
    }>;
    deleteOldLogs(olderThanDays?: number): Promise<number>;
    private mapRowToLog;
}
declare const _default: WebhookLogModel;
export default _default;
//# sourceMappingURL=webhookLogModel.d.ts.map