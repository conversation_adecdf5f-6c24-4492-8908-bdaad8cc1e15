{"name": "sms-be", "version": "1.0.0", "description": "Node.js API application for Android SMS Gateway integration with LINE Bot", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:setup": "node scripts/test-setup.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "setup": "npm install && npm run build", "clean": "rm -rf dist node_modules"}, "keywords": ["sms", "android", "gateway", "line", "bot", "api", "nodejs", "typescript"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@line/bot-sdk": "^10.0.0", "android-sms-gateway": "^3.0.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.1", "sqlite3": "^5.1.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.14", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "jest": "^30.0.4", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}