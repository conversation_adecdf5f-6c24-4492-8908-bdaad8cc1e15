{"timestamp":"2025-07-18T07:08:08.653Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:08:11.486Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:08:11.594Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:08:11.595Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:08:11.599Z","level":"ERROR","message":"Uncaught Exception","meta":{"error":"app is not defined","stack":"ReferenceError: app is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\sms-be\\src\\index.ts:75:16)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1565:14)\n    at Module._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\sms-be\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-3214583874898931.js:69:33)\n    at node:internal/modules/cjs/loader:1708:10\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-3214583874898931.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-3214583874898931.js:71:20)\n    at Object.nodeDevHook [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\sms-be\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)"}}
{"timestamp":"2025-07-18T07:10:36.704Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:10:36.937Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:10:37.040Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:10:37.040Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:10:37.042Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:10:37.079Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:10:37.080Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:10:37.080Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:10:37.083Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:10:37.084Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:10:42.089Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:10:42.778Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:10:42.779Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:10:42.900Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:10:42.901Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:10:42.902Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:10:42.902Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:10:42.903Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:11:12.220Z","level":"INFO","message":"GET /health","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}}
{"timestamp":"2025-07-18T07:11:12.223Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:11:12 +0000] \"GET /health HTTP/1.1\" 200 112 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\""}
{"timestamp":"2025-07-18T07:11:22.370Z","level":"INFO","message":"GET /api","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}}
{"timestamp":"2025-07-18T07:11:22.372Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:11:22 +0000] \"GET /api HTTP/1.1\" 200 397 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\""}
{"timestamp":"2025-07-18T07:12:02.674Z","level":"INFO","message":"GET /health","meta":{"ip":"::1","userAgent":"SMS-Gateway-Test-Script/1.0"}}
{"timestamp":"2025-07-18T07:12:02.675Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:12:02 +0000] \"GET /health HTTP/1.1\" 200 112 \"-\" \"SMS-Gateway-Test-Script/1.0\""}
{"timestamp":"2025-07-18T07:12:02.677Z","level":"INFO","message":"GET /api/sms/health","meta":{"ip":"::1","userAgent":"SMS-Gateway-Test-Script/1.0"}}
{"timestamp":"2025-07-18T07:12:02.679Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:12:02 +0000] \"GET /api/sms/health HTTP/1.1\" 200 124 \"-\" \"SMS-Gateway-Test-Script/1.0\""}
{"timestamp":"2025-07-18T07:12:02.679Z","level":"INFO","message":"GET /api/line/health","meta":{"ip":"::1","userAgent":"SMS-Gateway-Test-Script/1.0"}}
{"timestamp":"2025-07-18T07:12:02.680Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:12:02 +0000] \"GET /api/line/health HTTP/1.1\" 200 146 \"-\" \"SMS-Gateway-Test-Script/1.0\""}
{"timestamp":"2025-07-18T07:12:02.681Z","level":"INFO","message":"GET /api/devices/health","meta":{"ip":"::1","userAgent":"SMS-Gateway-Test-Script/1.0"}}
{"timestamp":"2025-07-18T07:12:04.187Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:12:04 +0000] \"GET /api/devices/health HTTP/1.1\" 200 181 \"-\" \"SMS-Gateway-Test-Script/1.0\""}
{"timestamp":"2025-07-18T07:12:04.189Z","level":"INFO","message":"GET /webhook/health","meta":{"ip":"::1","userAgent":"SMS-Gateway-Test-Script/1.0"}}
{"timestamp":"2025-07-18T07:12:04.190Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:12:04 +0000] \"GET /webhook/health HTTP/1.1\" 200 189 \"-\" \"SMS-Gateway-Test-Script/1.0\""}
{"timestamp":"2025-07-18T07:12:04.191Z","level":"INFO","message":"GET /api","meta":{"ip":"::1","userAgent":"SMS-Gateway-Test-Script/1.0"}}
{"timestamp":"2025-07-18T07:12:04.191Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:12:04 +0000] \"GET /api HTTP/1.1\" 200 397 \"-\" \"SMS-Gateway-Test-Script/1.0\""}
{"timestamp":"2025-07-18T07:13:34.630Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:13:34.848Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:13:34.953Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:13:34.954Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:13:34.956Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:13:34.960Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:13:34.960Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:13:34.960Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:13:34.963Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:13:34.963Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:13:39.932Z","level":"INFO","message":"GET /","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:13:39.933Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:13:39 +0000] \"GET / HTTP/1.1\" 404 45 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:13:39.965Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:13:40.056Z","level":"INFO","message":"GET /favicon.ico","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:13:40.057Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:13:40 +0000] \"GET /favicon.ico HTTP/1.1\" 404 56 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:13:40.424Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:13:40.424Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:13:40.573Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:13:40.574Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:13:40.575Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:13:40.576Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:13:40.577Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:14:35.595Z","level":"INFO","message":"GET /api/auth/signin","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:14:35.595Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:14:35 +0000] \"GET /api/auth/signin HTTP/1.1\" 404 60 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:14:37.990Z","level":"INFO","message":"GET /api/","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:14:37.992Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:14:37 +0000] \"GET /api/ HTTP/1.1\" 200 397 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:14:49.253Z","level":"INFO","message":"GET /api/devices","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:14:49.677Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:14:49 +0000] \"GET /api/devices HTTP/1.1\" 200 200 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:15:09.065Z","level":"INFO","message":"GET /health","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:15:09.066Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:15:09 +0000] \"GET /health HTTP/1.1\" 200 112 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:16:36.537Z","level":"INFO","message":"SIGINT received, shutting down gracefully"}
{"timestamp":"2025-07-18T07:18:19.300Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:18:19.408Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:18:19.413Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:18:19.414Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:18:19.416Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:18:19.419Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:18:19.419Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:18:19.420Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:18:19.423Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:18:19.423Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:18:24.434Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:18:25.142Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:18:25.143Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:18:25.279Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:18:25.280Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:18:25.280Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:18:25.280Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:18:25.280Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:19:28.810Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:19:28.914Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:19:28.919Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:19:28.920Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:19:28.922Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:19:28.926Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:19:28.926Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:19:28.927Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:19:28.930Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:19:28.930Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:19:33.939Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:19:34.423Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:19:34.424Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:19:34.574Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:19:34.574Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:19:34.574Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:19:34.575Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:19:34.575Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:19:56.759Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:19:56.864Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:19:56.869Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:19:56.869Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:19:56.871Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:19:56.880Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:19:56.881Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:19:56.881Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:19:56.887Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:19:56.888Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:20:01.889Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:20:02.373Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:20:02.374Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:20:02.514Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:20:02.514Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:20:02.515Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:20:02.515Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:20:02.515Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:20:40.606Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:20:40.711Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:20:40.717Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:20:40.717Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:20:40.720Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:20:40.723Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:20:40.723Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:20:40.723Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:20:40.726Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:20:40.727Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:20:45.740Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:20:46.125Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:20:46.126Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:20:46.247Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:20:46.248Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:20:46.248Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:20:46.248Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:20:46.248Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:20:56.898Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:20:57.006Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:20:57.012Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:20:57.012Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:20:57.015Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:20:57.017Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:20:57.017Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:20:57.017Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:20:57.020Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:20:57.021Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:21:02.027Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:21:02.466Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:21:02.466Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:21:02.603Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:21:02.604Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:21:02.604Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:21:02.604Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:21:02.604Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:21:40.336Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:21:40.443Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:21:40.448Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:21:40.449Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:21:40.451Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:21:40.453Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:21:40.453Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:21:40.454Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:21:40.458Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:21:40.459Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:21:45.458Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:21:45.872Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:21:45.872Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:21:46.000Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:21:46.001Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:21:46.002Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:21:46.002Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:21:46.003Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:22:18.136Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:22:18.243Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:22:18.249Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:22:18.249Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:22:18.252Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:22:18.254Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:22:18.254Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:22:18.254Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:22:18.258Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:22:18.259Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:22:23.268Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:22:24.008Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:22:24.009Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:22:24.138Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:22:24.140Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:22:24.140Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:22:24.142Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:22:24.143Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:22:43.443Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:22:43.548Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:22:43.554Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:22:43.554Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:22:43.557Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:22:43.559Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:22:43.559Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:22:43.559Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:22:43.563Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:22:43.563Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:22:48.577Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:22:48.982Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:22:48.983Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:22:49.100Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:22:49.100Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:22:49.101Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:22:49.101Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:22:49.101Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:23:00.924Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:23:01.028Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:23:01.034Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:23:01.034Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:23:01.037Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:23:01.039Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:23:01.039Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:23:01.039Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:23:01.043Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:23:01.043Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:23:06.054Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:23:06.541Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:23:06.541Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:23:06.714Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:23:06.715Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:23:06.715Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:23:06.715Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:23:06.715Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:23:51.514Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:23:51.620Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:23:51.626Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:23:51.626Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:23:51.629Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:23:51.630Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:23:51.631Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:23:51.631Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:23:51.635Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:23:51.635Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:23:56.643Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:23:57.279Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:23:57.280Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:23:57.415Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:23:57.416Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:23:57.416Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:23:57.416Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:23:57.416Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:24:27.550Z","level":"INFO","message":"GET /api-docs","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:24:27.554Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:24:27 +0000] \"GET /api-docs HTTP/1.1\" 301 158 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:24:27.558Z","level":"INFO","message":"GET /api-docs/","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:24:27.560Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:24:27 +0000] \"GET /api-docs/ HTTP/1.1\" 200 3094 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:24:27.603Z","level":"INFO","message":"GET /api-docs/swagger-ui.css","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:24:27.606Z","level":"INFO","message":"GET /api-docs/swagger-ui-bundle.js","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:24:27.607Z","level":"INFO","message":"GET /api-docs/swagger-ui-standalone-preset.js","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:24:27.608Z","level":"INFO","message":"GET /api-docs/swagger-ui-init.js","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:24:27.609Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:24:27 +0000] \"GET /api-docs/swagger-ui-init.js HTTP/1.1\" 200 36653 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:24:27.613Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:24:27 +0000] \"GET /api-docs/swagger-ui.css HTTP/1.1\" 200 155212 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:24:27.615Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:24:27 +0000] \"GET /api-docs/swagger-ui-standalone-preset.js HTTP/1.1\" 200 229223 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:24:27.619Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:24:27 +0000] \"GET /api-docs/swagger-ui-bundle.js HTTP/1.1\" 200 1485855 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:24:27.779Z","level":"INFO","message":"GET /api-docs/favicon-32x32.png","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:24:27.780Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:24:27 +0000] \"GET /api-docs/favicon-32x32.png HTTP/1.1\" 200 628 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:24:48.378Z","level":"INFO","message":"GET /api/sms/health","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:24:48.380Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:24:48 +0000] \"GET /api/sms/health HTTP/1.1\" 200 124 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:24:50.649Z","level":"INFO","message":"GET /api/sms/health","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:24:50.652Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:24:50 +0000] \"GET /api/sms/health HTTP/1.1\" 200 124 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:25:05.339Z","level":"INFO","message":"GET /api/sms/messages","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:25:05.342Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:25:05 +0000] \"GET /api/sms/messages?page=1&limit=20&status=Delivered&lineUserId=U503f97b9790a1fab78392736528b53ab&dateFrom=2024-01-01T00%3A00%3A00Z&dateTo=2024-01-31T23%3A59%3A59Z HTTP/1.1\" 200 86 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:25:20.492Z","level":"INFO","message":"GET /api/devices/settings","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:25:20.892Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:25:20 +0000] \"GET /api/devices/settings HTTP/1.1\" 200 26 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:25:25.400Z","level":"INFO","message":"GET /api/devices","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}}
{"timestamp":"2025-07-18T07:25:25.802Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:25:25 +0000] \"GET /api/devices HTTP/1.1\" 200 200 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:25:57.169Z","level":"INFO","message":"POST /api/line/send","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","body":{"userId":"U503f97b9790a1fab78392736528b53ab","message":{"type":"text","text":"Hello from API!"}}}}
{"timestamp":"2025-07-18T07:25:57.169Z","level":"INFO","message":"Send LINE message request","meta":{"userId":"U503f97b9790a1fab78392736528b53ab","messageType":"object"}}
{"timestamp":"2025-07-18T07:25:57.559Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:25:57 +0000] \"POST /api/line/send HTTP/1.1\" 200 54 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:26:27.866Z","level":"INFO","message":"POST /api/line/broadcast","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","body":{"message":{"type":"text","text":"Broadcast message to all users"}}}}
{"timestamp":"2025-07-18T07:26:27.867Z","level":"INFO","message":"Broadcast LINE message request","meta":{"messageType":"object"}}
{"timestamp":"2025-07-18T07:26:28.222Z","level":"INFO","message":"Broadcast message sent"}
{"timestamp":"2025-07-18T07:26:28.231Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:26:28 +0000] \"POST /api/line/broadcast HTTP/1.1\" 200 64 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\""}
{"timestamp":"2025-07-18T07:45:27.821Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:45:27.933Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:45:27.938Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:45:27.939Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:45:27.941Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:45:27.943Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:45:27.943Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:45:27.943Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:45:27.947Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:45:27.948Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:45:32.953Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:45:33.490Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:45:33.490Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:45:33.610Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:45:33.610Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:45:33.611Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:45:33.611Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:45:33.611Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:46:06.163Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:46:06.273Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:46:06.279Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:46:06.279Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:46:06.282Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:46:06.284Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:46:06.284Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:46:06.285Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:46:06.288Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:46:06.288Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:46:11.299Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:46:11.719Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:46:11.719Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:46:11.840Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:46:11.841Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:46:11.842Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:46:11.842Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:46:11.843Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:46:40.438Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:46:40.540Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:46:40.547Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:46:40.547Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:46:40.550Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:46:40.551Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:46:40.551Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:46:40.552Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:46:40.555Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:46:40.556Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:46:45.563Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:46:46.036Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:46:46.037Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:46:46.181Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:46:46.182Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:46:46.183Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:46:46.183Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:46:46.184Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:46:54.726Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:46:54.829Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:46:54.835Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:46:54.836Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:46:54.838Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:46:54.840Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:46:54.840Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:46:54.840Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:46:54.844Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:46:54.844Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:46:59.849Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:47:00.339Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:47:00.339Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:47:00.495Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:00.496Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:00.496Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:47:00.496Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:00.497Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:10.821Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:47:10.922Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:47:10.928Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:47:10.929Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:47:10.931Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:47:10.934Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:47:10.934Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:47:10.934Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:47:10.938Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:47:10.938Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:47:15.952Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:47:16.371Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:47:16.372Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:47:16.492Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:16.492Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:16.493Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:47:16.493Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:16.494Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:26.280Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T07:47:26.388Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T07:47:26.394Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T07:47:26.395Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T07:47:26.397Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T07:47:26.399Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T07:47:26.399Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T07:47:26.400Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T07:47:26.403Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T07:47:26.404Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T07:47:31.406Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T07:47:31.832Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T07:47:31.833Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T07:47:31.955Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:31.957Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:31.958Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T07:47:31.959Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:47:31.960Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T07:49:47.413Z","level":"INFO","message":"GET /api-docs/","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}}
{"timestamp":"2025-07-18T07:49:47.417Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:49:47 +0000] \"GET /api-docs/ HTTP/1.1\" 200 3094 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\""}
{"timestamp":"2025-07-18T07:50:32.321Z","level":"INFO","message":"POST /api/sms/send","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652","body":{"phoneNumbers":["+**********"],"message":"Test message from fixed API!"}}}
{"timestamp":"2025-07-18T07:50:32.321Z","level":"INFO","message":"SMS send request received","meta":{"phoneNumbers":["+**********"],"messageLength":28}}
{"timestamp":"2025-07-18T07:50:32.322Z","level":"INFO","message":"Sending SMS message","meta":{"phoneNumbers":["+**********"],"messageLength":28}}
{"timestamp":"2025-07-18T07:50:32.340Z","level":"INFO","message":"Message created in database","meta":{"messageId":"5506bad5-ff51-4c72-bd3b-dd759a032beb","phoneNumbers":["+**********"],"messageLength":28}}
{"timestamp":"2025-07-18T07:50:32.340Z","level":"INFO","message":"Sending SMS message","meta":{"phoneNumbers":["+**********"],"messageLength":28,"options":{}}}
{"timestamp":"2025-07-18T07:50:33.007Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/message","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"invalid phone number\"}"}}
{"timestamp":"2025-07-18T07:50:33.009Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/message","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"invalid phone number\"}"}}
{"timestamp":"2025-07-18T07:50:33.009Z","level":"ERROR","message":"Failed to send SMS message","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"invalid phone number\"}","phoneNumbers":["+**********"]}}
{"timestamp":"2025-07-18T07:50:33.010Z","level":"ERROR","message":"Failed to send SMS message","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"invalid phone number\"}","phoneNumbers":["+**********"]}}
{"timestamp":"2025-07-18T07:50:33.020Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:50:33 +0000] \"POST /api/sms/send HTTP/1.1\" 500 515 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\""}
{"timestamp":"2025-07-18T07:50:55.989Z","level":"INFO","message":"GET /health","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}}
{"timestamp":"2025-07-18T07:50:55.990Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:50:55 +0000] \"GET /health HTTP/1.1\" 200 112 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\""}
{"timestamp":"2025-07-18T07:51:09.403Z","level":"INFO","message":"GET /api","meta":{"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}}
{"timestamp":"2025-07-18T07:51:09.404Z","level":"INFO","message":"::1 - - [18/Jul/2025:07:51:09 +0000] \"GET /api HTTP/1.1\" 200 524 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\""}
{"timestamp":"2025-07-18T09:11:48.988Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T09:11:49.101Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T09:11:49.106Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T09:11:49.107Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T09:11:49.109Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T09:11:49.111Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T09:11:49.111Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T09:11:49.112Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T09:11:49.115Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T09:11:49.116Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T09:11:54.117Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T09:11:54.888Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T09:11:54.889Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T09:11:55.034Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:11:55.035Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:11:55.036Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T09:11:55.037Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:11:55.038Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:12:01.845Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T09:12:01.949Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T09:12:01.954Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T09:12:01.955Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T09:12:01.957Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T09:12:01.959Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T09:12:01.959Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T09:12:01.959Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T09:12:01.963Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T09:12:01.963Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T09:12:06.971Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T09:12:07.405Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T09:12:07.406Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T09:12:07.533Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:12:07.533Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:12:07.534Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T09:12:07.534Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:12:07.534Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:12:16.543Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T09:12:16.649Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T09:12:16.655Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T09:12:16.655Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T09:12:16.658Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T09:12:16.659Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T09:12:16.660Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T09:12:16.660Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T09:12:16.665Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T09:12:16.665Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T09:12:21.669Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T09:12:22.347Z","level":"INFO","message":"SMS Gateway client initialized","meta":{"baseUrl":"https://smsapi.tddaij.com/api/3rdparty/v1","login":"WFQSAG"}}
{"timestamp":"2025-07-18T09:12:22.453Z","level":"INFO","message":"LINE Bot client initialized","meta":{"channelAccessToken":"configured","channelSecret":"configured"}}
{"timestamp":"2025-07-18T09:12:22.459Z","level":"INFO","message":"Configuration validation passed"}
{"timestamp":"2025-07-18T09:12:22.460Z","level":"INFO","message":"Initializing database..."}
{"timestamp":"2025-07-18T09:12:22.462Z","level":"INFO","message":"Connected to SQLite database","meta":{"path":"./data/sms-gateway.db"}}
{"timestamp":"2025-07-18T09:12:22.464Z","level":"INFO","message":"Database tables and indexes initialized successfully"}
{"timestamp":"2025-07-18T09:12:22.464Z","level":"INFO","message":"Database migrations completed"}
{"timestamp":"2025-07-18T09:12:22.465Z","level":"INFO","message":"Database initialized successfully"}
{"timestamp":"2025-07-18T09:12:22.469Z","level":"INFO","message":"SMS Gateway API started successfully","meta":{"nodeEnv":"development","port":3000}}
{"timestamp":"2025-07-18T09:12:22.469Z","level":"INFO","message":"Server is running on port 3000","meta":{"environment":"development","baseUrl":"http://localhost:3000"}}
{"timestamp":"2025-07-18T09:12:27.483Z","level":"INFO","message":"Setting up webhooks"}
{"timestamp":"2025-07-18T09:12:27.924Z","level":"INFO","message":"Registering SMS webhook","meta":{"webhookUrl":"http://localhost:3000/webhook/sms"}}
{"timestamp":"2025-07-18T09:12:27.925Z","level":"INFO","message":"Registering webhook","meta":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}
{"timestamp":"2025-07-18T09:12:28.058Z","level":"ERROR","message":"HTTP 400 Bad Request","meta":{"url":"https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","method":"POST","status":400,"statusText":"Bad Request","body":"{\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:12:28.058Z","level":"ERROR","message":"HTTP request failed: https://smsapi.tddaij.com/api/3rdparty/v1/webhooks","meta":{"method":"POST","error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:12:28.058Z","level":"ERROR","message":"Failed to register webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}","request":{"url":"http://localhost:3000/webhook/sms","event":"sms:received"}}}
{"timestamp":"2025-07-18T09:12:28.058Z","level":"ERROR","message":"Failed to register SMS webhook","meta":{"error":"HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
{"timestamp":"2025-07-18T09:12:28.059Z","level":"ERROR","message":"Failed to setup webhooks","meta":{"error":"Failed to register SMS webhook: HTTP 400: Bad Request - {\"message\":\"validation failed: url must start with https://\"}"}}
