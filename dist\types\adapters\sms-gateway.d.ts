import * as LibraryTypes from 'android-sms-gateway';
import { SMSMessage, MessageState, RecipientState } from '../sms/message';
import { WebHook, RegisterWebHookRequest } from '../sms/webhook';
import { MessagesExportRequest } from '../sms/gateway';
import { DeviceSettings } from '../device/settings';
import { Device } from '../device/device';
export type LibraryMessage = LibraryTypes.Message;
export type LibraryMessageState = LibraryTypes.MessageState;
export type LibraryRecipientState = LibraryTypes.RecipientState;
export type LibraryWebHook = LibraryTypes.WebHook;
export type LibraryRegisterWebHookRequest = LibraryTypes.RegisterWebHookRequest;
export type LibraryDevice = LibraryTypes.Device;
export type LibraryDeviceSettings = LibraryTypes.DeviceSettings;
export type LibraryLimitPeriod = LibraryTypes.LimitPeriod;
export type LibraryMessagesExportRequest = LibraryTypes.MessagesExportRequest;
export declare namespace Adapters {
    function toLibraryMessage(message: SMSMessage): LibraryMessage;
    function fromLibraryMessageState(state: LibraryMessageState): MessageState;
    function fromLibraryRecipientState(recipient: LibraryRecipientState): RecipientState;
    function toLibraryRegisterWebHookRequest(request: RegisterWebHookRequest): LibraryRegisterWebHookRequest;
    function fromLibraryWebHook(webhook: LibraryWebHook): WebHook;
    function toLibraryMessagesExportRequest(request: MessagesExportRequest): LibraryMessagesExportRequest;
    function fromLibraryDevice(device: LibraryDevice): Device;
    function toLibraryDeviceSettings(settings: DeviceSettings): LibraryDeviceSettings;
    function fromLibraryDeviceSettings(settings: LibraryDeviceSettings): DeviceSettings;
}
//# sourceMappingURL=sms-gateway.d.ts.map