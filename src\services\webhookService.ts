import smsGatewayClient from './smsGatewayClient';
import messageModel from '../models/messageModel';
import webhookLogModel from '../models/webhookLogModel';
import lineService from './lineService';
import { WebHook, WebHookEventType, ProcessState } from '../types';
import config from '../config';
import logger from '../utils/logger';
import { AppError } from '../middleware/errorHandler';

interface SMSWebhookPayload {
  event: string;
  messageId?: string;
  deviceId: string;
  phoneNumber?: string;
  message?: string;
  status?: string;
  timestamp: string;
  error?: string;
}

class WebhookService {
  /**
   * Register webhook with SMS Gateway
   */
  async registerSMSWebhook(): Promise<WebHook> {
    try {
      const webhookUrl = `${config.server.baseUrl}${config.webhooks.smsPath}`;
      
      logger.info('Registering SMS webhook', { webhookUrl });

      const webhook = await smsGatewayClient.registerWebhook({
        url: webhookUrl,
        event: WebHookEventType.SmsReceived,
      });

      logger.info('SMS webhook registered successfully', {
        webhookId: webhook.id,
        url: webhook.url,
        event: webhook.event,
      });

      return webhook;
    } catch (error) {
      logger.error('Failed to register SMS webhook', {
        error: (error as Error).message,
      });

      throw new AppError(
        `Failed to register SMS webhook: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get registered webhooks
   */
  async getWebhooks(): Promise<WebHook[]> {
    try {
      logger.debug('Getting registered webhooks');

      const webhooks = await smsGatewayClient.getWebhooks();

      logger.debug('Webhooks retrieved', { count: webhooks.length });

      return webhooks;
    } catch (error) {
      logger.error('Failed to get webhooks', {
        error: (error as Error).message,
      });

      throw new AppError(
        `Failed to get webhooks: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Delete webhook
   */
  async deleteWebhook(webhookId: string): Promise<void> {
    try {
      logger.info('Deleting webhook', { webhookId });

      await smsGatewayClient.deleteWebhook(webhookId);

      logger.info('Webhook deleted successfully', { webhookId });
    } catch (error) {
      logger.error('Failed to delete webhook', {
        error: (error as Error).message,
        webhookId,
      });

      throw new AppError(
        `Failed to delete webhook: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Handle SMS webhook payload
   */
  async handleSMSWebhook(payload: SMSWebhookPayload): Promise<void> {
    try {
      logger.info('Processing SMS webhook', {
        event: payload.event,
        messageId: payload.messageId,
        deviceId: payload.deviceId,
        phoneNumber: payload.phoneNumber,
      });

      // Log webhook for debugging
      const webhookLog = await webhookLogModel.create({
        type: 'sms',
        payload,
      });

      // Process different event types
      switch (payload.event) {
        case 'sms:received':
          await this.handleSMSReceived(payload);
          break;
        case 'sms:sent':
          await this.handleSMSSent(payload);
          break;
        case 'sms:delivered':
          await this.handleSMSDelivered(payload);
          break;
        case 'sms:failed':
          await this.handleSMSFailed(payload);
          break;
        default:
          logger.warn('Unknown SMS webhook event', { event: payload.event });
      }

      // Mark webhook as processed
      await webhookLogModel.markAsProcessed(webhookLog.id);

      logger.info('SMS webhook processed successfully', {
        event: payload.event,
        webhookLogId: webhookLog.id,
      });
    } catch (error) {
      logger.error('Failed to process SMS webhook', {
        error: (error as Error).message,
        payload,
      });

      throw error;
    }
  }

  /**
   * Handle SMS received event
   */
  private async handleSMSReceived(payload: SMSWebhookPayload): Promise<void> {
    try {
      logger.info('Processing SMS received event', {
        deviceId: payload.deviceId,
        phoneNumber: payload.phoneNumber,
        message: payload.message?.substring(0, 100),
      });

      // Send notification to LINE Bot admins
      const message = {
        type: 'text' as const,
        text: `📨 New SMS Received

From: ${payload.phoneNumber}
Device: ${payload.deviceId}
Time: ${new Date(payload.timestamp).toLocaleString()}

Message:
${payload.message}`,
      };

      await lineService.sendToAdmins(message);

      logger.info('SMS received notification sent to admins');
    } catch (error) {
      logger.error('Failed to handle SMS received event', {
        error: (error as Error).message,
        payload,
      });
    }
  }

  /**
   * Handle SMS sent event
   */
  private async handleSMSSent(payload: SMSWebhookPayload): Promise<void> {
    try {
      if (!payload.messageId) {
        logger.warn('SMS sent event without messageId', { payload });
        return;
      }

      logger.info('Processing SMS sent event', {
        messageId: payload.messageId,
        phoneNumber: payload.phoneNumber,
      });

      // Update message status in database
      await messageModel.update(payload.messageId, {
        status: ProcessState.Sent,
      });

      if (payload.phoneNumber) {
        await messageModel.updateRecipientStatus(
          payload.messageId,
          payload.phoneNumber,
          ProcessState.Sent
        );
      }

      // Get message details to notify LINE user
      const messageDetails = await messageModel.findById(payload.messageId);
      if (messageDetails?.lineUserId) {
        const statusMessage = {
          type: 'text' as const,
          text: `📤 SMS Sent Successfully

To: ${payload.phoneNumber}
Message ID: ${payload.messageId}
Time: ${new Date(payload.timestamp).toLocaleString()}

Status: Sent ✅`,
        };

        await lineService.pushMessage(messageDetails.lineUserId, statusMessage);
      }

      logger.info('SMS sent event processed successfully');
    } catch (error) {
      logger.error('Failed to handle SMS sent event', {
        error: (error as Error).message,
        payload,
      });
    }
  }

  /**
   * Handle SMS delivered event
   */
  private async handleSMSDelivered(payload: SMSWebhookPayload): Promise<void> {
    try {
      if (!payload.messageId) {
        logger.warn('SMS delivered event without messageId', { payload });
        return;
      }

      logger.info('Processing SMS delivered event', {
        messageId: payload.messageId,
        phoneNumber: payload.phoneNumber,
      });

      // Update message status in database
      await messageModel.update(payload.messageId, {
        status: ProcessState.Delivered,
        deliveredAt: new Date(payload.timestamp),
      });

      if (payload.phoneNumber) {
        await messageModel.updateRecipientStatus(
          payload.messageId,
          payload.phoneNumber,
          ProcessState.Delivered
        );
      }

      // Get message details to notify LINE user
      const messageDetails = await messageModel.findById(payload.messageId);
      if (messageDetails?.lineUserId) {
        const statusMessage = {
          type: 'text' as const,
          text: `✅ SMS Delivered Successfully

To: ${payload.phoneNumber}
Message ID: ${payload.messageId}
Delivered: ${new Date(payload.timestamp).toLocaleString()}

Status: Delivered ✅`,
        };

        await lineService.pushMessage(messageDetails.lineUserId, statusMessage);
      }

      logger.info('SMS delivered event processed successfully');
    } catch (error) {
      logger.error('Failed to handle SMS delivered event', {
        error: (error as Error).message,
        payload,
      });
    }
  }

  /**
   * Handle SMS failed event
   */
  private async handleSMSFailed(payload: SMSWebhookPayload): Promise<void> {
    try {
      if (!payload.messageId) {
        logger.warn('SMS failed event without messageId', { payload });
        return;
      }

      logger.info('Processing SMS failed event', {
        messageId: payload.messageId,
        phoneNumber: payload.phoneNumber,
        error: payload.error,
      });

      // Update message status in database
      await messageModel.update(payload.messageId, {
        status: ProcessState.Failed,
        failedReason: payload.error || 'Unknown error',
      });

      if (payload.phoneNumber) {
        await messageModel.updateRecipientStatus(
          payload.messageId,
          payload.phoneNumber,
          ProcessState.Failed,
          payload.error
        );
      }

      // Get message details to notify LINE user
      const messageDetails = await messageModel.findById(payload.messageId);
      if (messageDetails?.lineUserId) {
        const statusMessage = {
          type: 'text' as const,
          text: `❌ SMS Failed to Send

To: ${payload.phoneNumber}
Message ID: ${payload.messageId}
Time: ${new Date(payload.timestamp).toLocaleString()}

Error: ${payload.error || 'Unknown error'}

Status: Failed ❌`,
        };

        await lineService.pushMessage(messageDetails.lineUserId, statusMessage);
      }

      logger.info('SMS failed event processed successfully');
    } catch (error) {
      logger.error('Failed to handle SMS failed event', {
        error: (error as Error).message,
        payload,
      });
    }
  }

  /**
   * Setup webhooks on startup
   */
  async setupWebhooks(): Promise<void> {
    try {
      logger.info('Setting up webhooks');

      // Check if webhook already exists
      const existingWebhooks = await this.getWebhooks();
      const webhookUrl = `${config.server.baseUrl}${config.webhooks.smsPath}`;
      
      const existingWebhook = existingWebhooks.find(w => w.url === webhookUrl);
      
      if (existingWebhook) {
        logger.info('SMS webhook already registered', {
          webhookId: existingWebhook.id,
          url: existingWebhook.url,
        });
      } else {
        await this.registerSMSWebhook();
      }

      logger.info('Webhooks setup completed');
    } catch (error) {
      logger.error('Failed to setup webhooks', {
        error: (error as Error).message,
      });
      // Don't throw error to prevent app startup failure
    }
  }
}

export default new WebhookService();
