"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const lineController_1 = __importDefault(require("../controllers/lineController"));
const validation_1 = require("../middleware/validation");
const config_1 = __importDefault(require("../config"));
const router = (0, express_1.Router)();
router.post('/', (0, validation_1.validateLineSignature)(config_1.default.lineBot.channelSecret), lineController_1.default.handleWebhook);
router.post('/send', lineController_1.default.sendMessage);
router.post('/broadcast', lineController_1.default.broadcastMessage);
router.post('/send-to-admins', lineController_1.default.sendToAdmins);
router.get('/info', lineController_1.default.getBotInfo);
router.get('/health', lineController_1.default.healthCheck);
exports.default = router;
//# sourceMappingURL=line.js.map