import { Send<PERSON><PERSON>e<PERSON>, SendSMSResponse, MessageState, StoredMessage, MessageFilter, PaginatedResponse } from '../types';
declare class SMSService {
    sendMessage(request: SendSMSRequest, lineUserId?: string): Promise<SendSMSResponse>;
    getMessageStatus(messageId: string): Promise<MessageState>;
    getMessages(filter?: MessageFilter, page?: number, limit?: number): Promise<PaginatedResponse<StoredMessage>>;
    getMessageDetails(messageId: string): Promise<{
        message: StoredMessage;
        recipients: any[];
    }>;
    refreshMessageStatus(messageId: string): Promise<MessageState>;
    getMessageStats(dateFrom?: Date, dateTo?: Date): Promise<{
        total: number;
        pending: number;
        sent: number;
        delivered: number;
        failed: number;
        byDate: Array<{
            date: string;
            count: number;
        }>;
    }>;
}
declare const _default: SMSService;
export default _default;
//# sourceMappingURL=smsService.d.ts.map