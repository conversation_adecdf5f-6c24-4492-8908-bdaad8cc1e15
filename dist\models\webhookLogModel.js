"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const uuid_1 = require("uuid");
const database_1 = __importDefault(require("./database"));
const logger_1 = __importDefault(require("../utils/logger"));
class WebhookLogModel {
    async create(data) {
        const id = (0, uuid_1.v4)();
        const now = new Date();
        const log = {
            id,
            type: data.type,
            payload: JSON.stringify(data.payload),
            processed: false,
            createdAt: now,
            updatedAt: now,
        };
        await database_1.default.run(`INSERT INTO webhook_logs (
        id, type, payload, processed, created_at
      ) VALUES (?, ?, ?, ?, ?)`, [
            log.id,
            log.type,
            log.payload,
            log.processed ? 1 : 0,
            log.createdAt.toISOString(),
        ]);
        logger_1.default.debug('Webhook log created in database', {
            logId: id,
            type: data.type,
            payloadSize: log.payload.length,
        });
        return log;
    }
    async findById(id) {
        const row = await database_1.default.get('SELECT * FROM webhook_logs WHERE id = ?', [id]);
        if (!row) {
            return null;
        }
        return this.mapRowToLog(row);
    }
    async update(id, data) {
        const updates = [];
        const values = [];
        if (data.processed !== undefined) {
            updates.push('processed = ?');
            values.push(data.processed ? 1 : 0);
            if (data.processed) {
                updates.push('processed_at = ?');
                values.push(new Date().toISOString());
            }
        }
        if (data.error !== undefined) {
            updates.push('error = ?');
            values.push(data.error);
        }
        if (updates.length === 0) {
            return;
        }
        values.push(id);
        await database_1.default.run(`UPDATE webhook_logs SET ${updates.join(', ')} WHERE id = ?`, values);
        logger_1.default.debug('Webhook log updated in database', { logId: id, updates: data });
    }
    async markAsProcessed(id, error) {
        await this.update(id, {
            processed: true,
            error,
        });
    }
    async findMany(filter = {}, pagination = { page: 1, limit: 50 }) {
        const conditions = [];
        const values = [];
        if (filter.type) {
            conditions.push('type = ?');
            values.push(filter.type);
        }
        if (filter.processed !== undefined) {
            conditions.push('processed = ?');
            values.push(filter.processed ? 1 : 0);
        }
        if (filter.dateFrom) {
            conditions.push('created_at >= ?');
            values.push(filter.dateFrom.toISOString());
        }
        if (filter.dateTo) {
            conditions.push('created_at <= ?');
            values.push(filter.dateTo.toISOString());
        }
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        const countResult = await database_1.default.get(`SELECT COUNT(*) as count FROM webhook_logs ${whereClause}`, values);
        const total = countResult?.count || 0;
        const offset = (pagination.page - 1) * pagination.limit;
        const rows = await database_1.default.all(`SELECT * FROM webhook_logs ${whereClause} 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`, [...values, pagination.limit, offset]);
        const logs = rows.map(row => this.mapRowToLog(row));
        return { logs, total };
    }
    async findUnprocessed(limit = 100) {
        const rows = await database_1.default.all('SELECT * FROM webhook_logs WHERE processed = 0 ORDER BY created_at ASC LIMIT ?', [limit]);
        return rows.map(row => this.mapRowToLog(row));
    }
    async getStats() {
        const totalResult = await database_1.default.get('SELECT COUNT(*) as count FROM webhook_logs');
        const processedResult = await database_1.default.get('SELECT COUNT(*) as count FROM webhook_logs WHERE processed = 1');
        const unprocessedResult = await database_1.default.get('SELECT COUNT(*) as count FROM webhook_logs WHERE processed = 0');
        const errorsResult = await database_1.default.get('SELECT COUNT(*) as count FROM webhook_logs WHERE error IS NOT NULL');
        const smsResult = await database_1.default.get('SELECT COUNT(*) as count FROM webhook_logs WHERE type = "sms"');
        const lineResult = await database_1.default.get('SELECT COUNT(*) as count FROM webhook_logs WHERE type = "line"');
        return {
            total: totalResult?.count || 0,
            processed: processedResult?.count || 0,
            unprocessed: unprocessedResult?.count || 0,
            errors: errorsResult?.count || 0,
            smsLogs: smsResult?.count || 0,
            lineLogs: lineResult?.count || 0,
        };
    }
    async deleteOldLogs(olderThanDays = 7) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
        const result = await database_1.default.run('DELETE FROM webhook_logs WHERE created_at < ? AND processed = 1', [cutoffDate.toISOString()]);
        logger_1.default.info('Old webhook logs deleted', {
            deletedCount: result.changes,
            cutoffDate: cutoffDate.toISOString(),
        });
        return result.changes || 0;
    }
    mapRowToLog(row) {
        return {
            id: row.id,
            type: row.type,
            payload: row.payload,
            processed: row.processed === 1,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at || row.created_at),
            processedAt: row.processed_at ? new Date(row.processed_at) : undefined,
            error: row.error || undefined,
        };
    }
}
exports.default = new WebhookLogModel();
//# sourceMappingURL=webhookLogModel.js.map