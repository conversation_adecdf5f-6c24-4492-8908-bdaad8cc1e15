import { Request, Response } from 'express';
import deviceService from '../services/deviceService';
import { DeviceSettings } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../utils/logger';

class DeviceController {
  /**
   * Get all devices
   * GET /api/devices
   */
  getDevices = asyncHandler(async (_req: Request, res: Response) => {
    logger.debug('Get devices request received');

    const devices = await deviceService.getDevices();

    res.json({
      success: true,
      data: devices,
    });
  });

  /**
   * Get device by ID
   * GET /api/devices/:deviceId
   */
  getDevice = asyncHandler(async (req: Request, res: Response) => {
    const { deviceId } = req.params;

    logger.debug('Get device request received', { deviceId });

    if (!deviceId) {
      return res.status(400).json({ error: 'Device ID is required' });
    }
    const device = await deviceService.getDevice(deviceId);

    res.json({
      success: true,
      data: device,
    });
  });

  /**
   * Delete device
   * DELETE /api/devices/:deviceId
   */
  deleteDevice = asyncHandler(async (req: Request, res: Response) => {
    const { deviceId } = req.params;

    logger.info('Delete device request received', { deviceId });

    if (!deviceId) {
      return res.status(400).json({ error: 'Device ID is required' });
    }
    await deviceService.deleteDevice(deviceId);

    res.json({
      success: true,
      message: 'Device deleted successfully',
    });
  });

  /**
   * Get device settings
   * GET /api/devices/settings
   */
  getSettings = asyncHandler(async (_req: Request, res: Response) => {
    logger.debug('Get device settings request received');

    const settings = await deviceService.getSettings();

    res.json({
      success: true,
      data: settings,
    });
  });

  /**
   * Update device settings (full update)
   * PUT /api/devices/settings
   */
  updateSettings = asyncHandler(async (req: Request, res: Response) => {
    const settings: DeviceSettings = req.body;

    logger.info('Update device settings request received', settings);

    await deviceService.updateSettings(settings);

    res.json({
      success: true,
      message: 'Device settings updated successfully',
    });
  });

  /**
   * Partially update device settings
   * PATCH /api/devices/settings
   */
  patchSettings = asyncHandler(async (req: Request, res: Response) => {
    const settings: Partial<DeviceSettings> = req.body;

    logger.info('Patch device settings request received', settings);

    await deviceService.patchSettings(settings);

    res.json({
      success: true,
      message: 'Device settings updated successfully',
    });
  });

  /**
   * Get system health
   * GET /api/devices/health
   */
  getHealth = asyncHandler(async (_req: Request, res: Response) => {
    logger.debug('Get system health request received');

    const health = await deviceService.getHealth();

    res.json({
      success: true,
      data: health,
    });
  });

  /**
   * Get system logs
   * GET /api/devices/logs
   */
  getLogs = asyncHandler(async (req: Request, res: Response) => {
    const { from, to } = req.query;

    const fromDate = from && typeof from === 'string' ? new Date(from) : undefined;
    const toDate = to && typeof to === 'string' ? new Date(to) : undefined;

    logger.debug('Get system logs request received', { from: fromDate, to: toDate });

    const logs = await deviceService.getLogs(fromDate, toDate);

    res.json({
      success: true,
      data: logs,
    });
  });

  /**
   * Export inbox messages
   * POST /api/devices/:deviceId/export-inbox
   */
  exportInbox = asyncHandler(async (req: Request, res: Response) => {
    const { deviceId } = req.params;
    const { since, until } = req.body;

    if (!since || !until) {
      return res.status(400).json({
        success: false,
        error: 'Both since and until dates are required',
      });
    }

    const sinceDate = new Date(since);
    const untilDate = new Date(until);

    logger.info('Export inbox request received', { deviceId, since: sinceDate, until: untilDate });

    if (!deviceId) {
      return res.status(400).json({ error: 'Device ID is required' });
    }
    await deviceService.exportInbox(deviceId, sinceDate, untilDate);

    res.json({
      success: true,
      message: 'Inbox export requested successfully',
    });
  });

  /**
   * Get device statistics
   * GET /api/devices/stats
   */
  getDeviceStats = asyncHandler(async (_req: Request, res: Response) => {
    logger.debug('Get device stats request received');

    const stats = await deviceService.getDeviceStats();

    res.json({
      success: true,
      data: stats,
    });
  });

  /**
   * Test device connection
   * POST /api/devices/test-connection
   */
  testConnection = asyncHandler(async (_req: Request, res: Response) => {
    logger.info('Test device connection request received');

    const isConnected = await deviceService.testConnection();

    res.json({
      success: true,
      data: {
        connected: isConnected,
        timestamp: new Date().toISOString(),
      },
      message: isConnected ? 'Device connection successful' : 'Device connection failed',
    });
  });
}

export default new DeviceController();
