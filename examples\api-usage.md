# API Usage Examples

This document provides practical examples of how to use the SMS Gateway API.

## Authentication

Currently, the API doesn't require authentication, but you should implement proper authentication in production.

## Sending SMS Messages

### Basic SMS Send

```bash
curl -X POST http://localhost:3000/api/sms/send \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumbers": ["+1234567890"],
    "message": "Hello from SMS Gateway API!"
  }'
```

Response:
```json
{
  "success": true,
  "data": {
    "messageId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "Sent",
    "recipients": [
      {
        "phoneNumber": "+1234567890",
        "state": "Sent"
      }
    ]
  },
  "message": "SMS message sent successfully"
}
```

### SMS with Delivery Report

```bash
curl -X POST http://localhost:3000/api/sms/send \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumbers": ["+1234567890", "+0987654321"],
    "message": "Hello to multiple recipients!",
    "withDeliveryReport": true,
    "ttl": 3600
  }'
```

### SMS from LINE Bot User

```bash
curl -X POST http://localhost:3000/api/sms/send \
  -H "Content-Type: application/json" \
  -H "X-Line-User-Id: line_user_id_here" \
  -d '{
    "phoneNumbers": ["+1234567890"],
    "message": "SMS sent from LINE Bot user"
  }'
```

## Checking Message Status

### Get Single Message Status

```bash
curl http://localhost:3000/api/sms/status/550e8400-e29b-41d4-a716-446655440000
```

Response:
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "state": "Delivered",
    "recipients": [
      {
        "phoneNumber": "+1234567890",
        "state": "Delivered"
      }
    ]
  }
}
```

### Refresh Message Status

```bash
curl -X POST http://localhost:3000/api/sms/status/550e8400-e29b-41d4-a716-446655440000/refresh
```

## Retrieving Messages

### Get All Messages (Paginated)

```bash
curl "http://localhost:3000/api/sms/messages?page=1&limit=10"
```

### Filter Messages by Status

```bash
curl "http://localhost:3000/api/sms/messages?status=Delivered&page=1&limit=20"
```

### Filter Messages by Phone Number

```bash
curl "http://localhost:3000/api/sms/messages?phoneNumber=%2B1234567890"
```

### Filter Messages by Date Range

```bash
curl "http://localhost:3000/api/sms/messages?dateFrom=2024-01-01&dateTo=2024-01-31"
```

### Get Message Details

```bash
curl http://localhost:3000/api/sms/messages/550e8400-e29b-41d4-a716-446655440000
```

Response:
```json
{
  "success": true,
  "data": {
    "message": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "phoneNumbers": "[\"+1234567890\"]",
      "message": "Hello from SMS Gateway API!",
      "status": "Delivered",
      "createdAt": "2024-01-01T12:00:00.000Z",
      "updatedAt": "2024-01-01T12:01:00.000Z",
      "deliveredAt": "2024-01-01T12:01:00.000Z"
    },
    "recipients": [
      {
        "phoneNumber": "+1234567890",
        "state": "Delivered"
      }
    ]
  }
}
```

## Device Management

### Get All Devices

```bash
curl http://localhost:3000/api/devices
```

### Get Device Settings

```bash
curl http://localhost:3000/api/devices/settings
```

### Update Device Settings

```bash
curl -X PUT http://localhost:3000/api/devices/settings \
  -H "Content-Type: application/json" \
  -d '{
    "messages": {
      "limitPeriod": "PerDay",
      "limitValue": 100
    },
    "webhooks": {
      "internetRequired": true,
      "retryCount": 3
    }
  }'
```

### Partially Update Settings

```bash
curl -X PATCH http://localhost:3000/api/devices/settings \
  -H "Content-Type: application/json" \
  -d '{
    "messages": {
      "limitValue": 200
    }
  }'
```

### Get System Health

```bash
curl http://localhost:3000/api/devices/health
```

### Get System Logs

```bash
curl "http://localhost:3000/api/devices/logs?from=2024-01-01T00:00:00Z&to=2024-01-02T00:00:00Z"
```

## Statistics

### Get Message Statistics

```bash
curl http://localhost:3000/api/sms/stats
```

Response:
```json
{
  "success": true,
  "data": {
    "total": 150,
    "pending": 5,
    "sent": 20,
    "delivered": 120,
    "failed": 5,
    "byDate": [
      {
        "date": "2024-01-01",
        "count": 50
      },
      {
        "date": "2024-01-02",
        "count": 100
      }
    ]
  }
}
```

### Get Device Statistics

```bash
curl http://localhost:3000/api/devices/stats
```

## LINE Bot Integration

### Send Message to LINE User

```bash
curl -X POST http://localhost:3000/api/line/send \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "line_user_id_here",
    "message": {
      "type": "text",
      "text": "Hello from API!"
    }
  }'
```

### Broadcast Message

```bash
curl -X POST http://localhost:3000/api/line/broadcast \
  -H "Content-Type: application/json" \
  -d '{
    "message": {
      "type": "text",
      "text": "Broadcast message to all users"
    }
  }'
```

### Send to Admin Users

```bash
curl -X POST http://localhost:3000/api/line/send-to-admins \
  -H "Content-Type: application/json" \
  -d '{
    "message": {
      "type": "text",
      "text": "Admin notification message"
    }
  }'
```

## Webhook Management

### Get Registered Webhooks

```bash
curl http://localhost:3000/api/webhooks
```

### Register New Webhook

```bash
curl -X POST http://localhost:3000/api/webhooks/register
```

### Delete Webhook

```bash
curl -X DELETE http://localhost:3000/api/webhooks/webhook_id_here
```

## Health Checks

### General Health Check

```bash
curl http://localhost:3000/health
```

### SMS Service Health

```bash
curl http://localhost:3000/api/sms/health
```

### LINE Bot Health

```bash
curl http://localhost:3000/api/line/health
```

### Device Health

```bash
curl http://localhost:3000/api/devices/health
```

### Webhook Health

```bash
curl http://localhost:3000/webhook/health
```

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "error": "Error message description"
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `404` - Not Found
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Rate Limiting

The API implements rate limiting:
- Default: 100 requests per 15 minutes per IP
- Headers included in response:
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time

## JavaScript/Node.js Examples

### Using fetch

```javascript
// Send SMS
async function sendSMS(phoneNumbers, message) {
  const response = await fetch('http://localhost:3000/api/sms/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      phoneNumbers,
      message,
      withDeliveryReport: true,
    }),
  });

  const result = await response.json();
  return result;
}

// Get message status
async function getMessageStatus(messageId) {
  const response = await fetch(`http://localhost:3000/api/sms/status/${messageId}`);
  const result = await response.json();
  return result;
}

// Usage
const result = await sendSMS(['+1234567890'], 'Hello from JavaScript!');
console.log('Message sent:', result.data.messageId);

const status = await getMessageStatus(result.data.messageId);
console.log('Message status:', status.data.state);
```

### Using axios

```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Send SMS
async function sendSMS(phoneNumbers, message) {
  try {
    const response = await api.post('/sms/send', {
      phoneNumbers,
      message,
      withDeliveryReport: true,
    });
    return response.data;
  } catch (error) {
    console.error('Error sending SMS:', error.response.data);
    throw error;
  }
}

// Get messages with filtering
async function getMessages(filters = {}) {
  try {
    const response = await api.get('/sms/messages', { params: filters });
    return response.data;
  } catch (error) {
    console.error('Error getting messages:', error.response.data);
    throw error;
  }
}
```
