import { BaseEntity } from '../base/common';
export interface Device extends BaseEntity {
    name: string;
    lastSeen: string;
    deletedAt?: string | null | undefined;
}
export interface CreateDeviceData {
    name: string;
    lastSeen?: string;
}
export interface UpdateDeviceData {
    name?: string;
    lastSeen?: string;
    deletedAt?: string | null;
}
export interface DeviceFilter {
    name?: string;
    active?: boolean;
    lastSeenAfter?: Date;
    lastSeenBefore?: Date;
}
//# sourceMappingURL=device.d.ts.map