# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_PATH=./data/sms-gateway.db

# Android SMS Gateway Configuration
SMS_GATEWAY_LOGIN=your_sms_gateway_username
SMS_GATEWAY_PASSWORD=your_sms_gateway_password
SMS_GATEWAY_BASE_URL=https://api.sms-gate.app/3rdparty/v1

# LINE Bot Configuration
LINE_CHANNEL_ACCESS_TOKEN=your_line_channel_access_token
LINE_CHANNEL_SECRET=your_line_channel_secret
LINE_WEBHOOK_PATH=/webhook/line

# SMS Gateway Webhook Configuration
SMS_WEBHOOK_PATH=/webhook/sms
SMS_WEBHOOK_SECRET=your_webhook_secret_key

# Application Configuration
APP_BASE_URL=http://localhost:3000
CORS_ORIGIN=*

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Security Configuration
JWT_SECRET=your_jwt_secret_key_here
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Message Configuration
DEFAULT_MESSAGE_TTL=3600
MAX_PHONE_NUMBERS_PER_REQUEST=10
MESSAGE_STATUS_CHECK_INTERVAL=30000

# LINE Bot Features
LINE_ADMIN_USER_ID=your_line_admin_user_id
LINE_QUICK_REPLY_ENABLED=true
LINE_RICH_MENU_ENABLED=true
