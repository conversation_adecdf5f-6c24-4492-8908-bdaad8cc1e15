"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const bot_sdk_1 = require("@line/bot-sdk");
const config_1 = __importDefault(require("../config"));
const lineUserModel_1 = __importDefault(require("../models/lineUserModel"));
const logger_1 = __importDefault(require("../utils/logger"));
class LineService {
    constructor() {
        const clientConfig = {
            channelAccessToken: config_1.default.lineBot.channelAccessToken,
            channelSecret: config_1.default.lineBot.channelSecret,
        };
        this.client = new bot_sdk_1.Client(clientConfig);
        logger_1.default.info('LINE Bot client initialized', {
            channelAccessToken: config_1.default.lineBot.channelAccessToken ? 'configured' : 'missing',
            channelSecret: config_1.default.lineBot.channelSecret ? 'configured' : 'missing',
        });
    }
    async handleWebhookEvents(events) {
        try {
            logger_1.default.info('Processing LINE webhook events', { count: events.length });
            for (const event of events) {
                await this.handleEvent(event);
            }
            logger_1.default.info('LINE webhook events processed successfully');
        }
        catch (error) {
            logger_1.default.error('Failed to handle LINE webhook events', {
                error: error.message,
                eventsCount: events.length,
            });
            throw error;
        }
    }
    async handleEvent(event) {
        try {
            logger_1.default.debug('Processing LINE event', { type: event.type, source: event.source });
            if (event.type === 'follow' || event.type === 'unfollow') {
                await this.handleUserEvent(event);
                return;
            }
            if (event.type === 'message') {
                await this.handleMessageEvent(event);
                return;
            }
            if (event.type === 'postback') {
                await this.handlePostbackEvent(event);
                return;
            }
            logger_1.default.debug('Unhandled LINE event type', { type: event.type });
        }
        catch (error) {
            logger_1.default.error('Failed to handle LINE event', {
                error: error.message,
                event,
            });
        }
    }
    async handleUserEvent(event) {
        const userId = event.source.userId;
        if (!userId) {
            logger_1.default.warn('User event without userId', { event });
            return;
        }
        try {
            if (event.type === 'follow') {
                const profile = await this.client.getProfile(userId);
                await lineUserModel_1.default.upsert({
                    id: userId,
                    displayName: profile.displayName,
                    pictureUrl: profile.pictureUrl,
                    statusMessage: profile.statusMessage,
                    isAdmin: userId === config_1.default.lineBot.adminUserId,
                });
                await this.sendWelcomeMessage(userId);
                logger_1.default.info('User followed bot', { userId, displayName: profile.displayName });
            }
            else if (event.type === 'unfollow') {
                logger_1.default.info('User unfollowed bot', { userId });
            }
        }
        catch (error) {
            logger_1.default.error('Failed to handle user event', {
                error: error.message,
                userId,
                eventType: event.type,
            });
        }
    }
    async handleMessageEvent(event) {
        const userId = event.source.userId;
        if (!userId) {
            logger_1.default.warn('Message event without userId', { event });
            return;
        }
        if (event.message.type !== 'text') {
            await this.replyMessage(event.replyToken, {
                type: 'text',
                text: 'Sorry, I can only handle text messages at the moment.',
            });
            return;
        }
        const textMessage = event.message;
        const messageText = textMessage.text.toLowerCase().trim();
        logger_1.default.info('Received LINE message', {
            userId,
            message: messageText,
            replyToken: event.replyToken,
        });
        if (messageText.startsWith('/send')) {
            await this.handleSendSMSCommand(event.replyToken, userId, messageText);
        }
        else if (messageText === '/status' || messageText === '/messages') {
            await this.handleStatusCommand(event.replyToken, userId);
        }
        else if (messageText === '/help' || messageText === '/menu') {
            await this.sendMainMenu(event.replyToken);
        }
        else {
            await this.sendMainMenu(event.replyToken);
        }
    }
    async handlePostbackEvent(event) {
        const userId = event.source.userId;
        const data = event.postback.data;
        logger_1.default.info('Received LINE postback', { userId, data });
        try {
            const action = JSON.parse(data);
            switch (action.type) {
                case 'send_sms':
                    await this.handleSendSMSAction(event.replyToken, userId, action.data);
                    break;
                case 'check_status':
                    await this.handleStatusCommand(event.replyToken, userId);
                    break;
                case 'view_messages':
                    await this.handleViewMessagesAction(event.replyToken, userId);
                    break;
                case 'settings':
                    await this.handleSettingsAction(event.replyToken, userId);
                    break;
                default:
                    await this.sendMainMenu(event.replyToken);
            }
        }
        catch (error) {
            logger_1.default.error('Failed to handle postback event', {
                error: error.message,
                userId,
                data,
            });
            await this.replyMessage(event.replyToken, {
                type: 'text',
                text: 'Sorry, something went wrong processing your request.',
            });
        }
    }
    async sendWelcomeMessage(userId) {
        const welcomeMessage = {
            type: 'text',
            text: `Welcome to SMS Gateway Bot! 🎉

I can help you send SMS messages and manage your SMS gateway. Here are some things you can do:

• Send SMS messages
• Check message status
• View recent messages
• Manage settings

Type /help or /menu to see all available options.`,
        };
        await this.pushMessage(userId, welcomeMessage);
    }
    async sendMainMenu(replyToken) {
        const quickReply = {
            items: [
                {
                    type: 'action',
                    action: {
                        type: 'postback',
                        label: '📱 Send SMS',
                        data: JSON.stringify({ type: 'send_sms' }),
                    },
                },
                {
                    type: 'action',
                    action: {
                        type: 'postback',
                        label: '📊 Check Status',
                        data: JSON.stringify({ type: 'check_status' }),
                    },
                },
                {
                    type: 'action',
                    action: {
                        type: 'postback',
                        label: '📋 View Messages',
                        data: JSON.stringify({ type: 'view_messages' }),
                    },
                },
                {
                    type: 'action',
                    action: {
                        type: 'postback',
                        label: '⚙️ Settings',
                        data: JSON.stringify({ type: 'settings' }),
                    },
                },
            ],
        };
        const message = {
            type: 'text',
            text: `SMS Gateway Bot Menu 🤖

What would you like to do?

Commands:
• /send [phone] [message] - Send SMS
• /status - Check recent messages
• /help - Show this menu`,
            quickReply,
        };
        await this.replyMessage(replyToken, message);
    }
    async handleSendSMSCommand(replyToken, userId, messageText) {
        const parts = messageText.split(' ');
        if (parts.length < 3) {
            await this.replyMessage(replyToken, {
                type: 'text',
                text: 'Usage: /send [phone_number] [message]\nExample: /send +1234567890 Hello world!',
            });
            return;
        }
        const phoneNumber = parts[1];
        const message = parts.slice(2).join(' ');
        if (!phoneNumber.startsWith('+')) {
            await this.replyMessage(replyToken, {
                type: 'text',
                text: 'Please provide a valid phone number starting with + (e.g., +1234567890)',
            });
            return;
        }
        await this.replyMessage(replyToken, {
            type: 'text',
            text: `📱 Sending SMS to ${phoneNumber}...\n\nMessage: ${message}`,
        });
        logger_1.default.info('SMS send request from LINE', { userId, phoneNumber, message });
    }
    async handleStatusCommand(replyToken, _userId) {
        await this.replyMessage(replyToken, {
            type: 'text',
            text: '📊 Recent SMS Status\n\nThis feature will show your recent SMS messages and their delivery status.',
        });
    }
    async handleSendSMSAction(replyToken, _userId, _data) {
        await this.replyMessage(replyToken, {
            type: 'text',
            text: '📱 Send SMS\n\nTo send an SMS, use the command:\n/send [phone_number] [message]\n\nExample:\n/send +1234567890 Hello world!',
        });
    }
    async handleViewMessagesAction(replyToken, _userId) {
        await this.replyMessage(replyToken, {
            type: 'text',
            text: '📋 Recent Messages\n\nThis will show your recent SMS messages and their status.',
        });
    }
    async handleSettingsAction(replyToken, _userId) {
        await this.replyMessage(replyToken, {
            type: 'text',
            text: '⚙️ Settings\n\nSettings management will be available here.',
        });
    }
    async replyMessage(replyToken, message) {
        try {
            await this.client.replyMessage(replyToken, message);
            logger_1.default.debug('Reply message sent', { replyToken });
        }
        catch (error) {
            logger_1.default.error('Failed to send reply message', {
                error: error.message,
                replyToken,
            });
            throw error;
        }
    }
    async pushMessage(userId, message) {
        try {
            await this.client.pushMessage(userId, message);
            logger_1.default.debug('Push message sent', { userId });
        }
        catch (error) {
            logger_1.default.error('Failed to send push message', {
                error: error.message,
                userId,
            });
            throw error;
        }
    }
    async broadcastMessage(message) {
        try {
            await this.client.broadcast(message);
            logger_1.default.info('Broadcast message sent');
        }
        catch (error) {
            logger_1.default.error('Failed to send broadcast message', {
                error: error.message,
            });
            throw error;
        }
    }
    async sendToAdmins(message) {
        try {
            const adminUsers = await lineUserModel_1.default.findAdmins();
            for (const admin of adminUsers) {
                await this.pushMessage(admin.id, message);
            }
            logger_1.default.info('Message sent to admins', { adminCount: adminUsers.length });
        }
        catch (error) {
            logger_1.default.error('Failed to send message to admins', {
                error: error.message,
            });
            throw error;
        }
    }
}
exports.default = new LineService();
//# sourceMappingURL=lineService.js.map