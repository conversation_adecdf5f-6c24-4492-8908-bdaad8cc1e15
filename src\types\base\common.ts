/**
 * Common base types and utility types used throughout the application
 */

// Base entity interface
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Pagination types
export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Process states
export enum ProcessState {
  Pending = 'Pending',
  Processed = 'Processed',
  Sent = 'Sent',
  Delivered = 'Delivered',
  Failed = 'Failed',
}

// Generic filter interface
export interface BaseFilter {
  dateFrom?: Date;
  dateTo?: Date;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type Nullable<T> = T | null;
export type OptionalNullable<T> = T | null | undefined;

// Database row mapping utility
export type DatabaseRow = Record<string, any>;

// Timestamp utilities
export interface TimestampFields {
  createdAt: Date;
  updatedAt: Date;
}

export interface OptionalTimestampFields {
  createdAt?: Date;
  updatedAt?: Date;
}
