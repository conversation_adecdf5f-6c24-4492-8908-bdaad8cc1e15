# LINE Bot Setup Guide

This guide will help you set up a LINE Bot to work with the SMS Gateway API.

## Prerequisites

- LINE account
- Access to LINE Developers Console
- SMS Gateway API running and accessible via HTTPS

## Step 1: Create a LINE Bot Channel

1. Go to [LINE Developers Console](https://developers.line.biz/)
2. Log in with your LINE account
3. Create a new provider (if you don't have one)
4. Click "Create a new channel"
5. Select "Messaging API"
6. Fill in the required information:
   - Channel name: "SMS Gateway Bot"
   - Channel description: "Bot for managing SMS messages"
   - Category: Choose appropriate category
   - Subcategory: Choose appropriate subcategory
7. Agree to the terms and create the channel

## Step 2: Configure Channel Settings

### Basic Settings

1. Go to the "Basic settings" tab
2. Note down your **Channel ID** and **Channel Secret**
3. Upload a channel icon (optional)
4. Set channel description

### Messaging API Settings

1. Go to the "Messaging API" tab
2. Generate a **Channel Access Token** (long-lived)
3. Note down the token - you'll need it for configuration
4. Set the webhook URL to: `https://your-domain.com/webhook/line`
5. Enable "Use webhook"
6. Disable "Auto-reply messages" (we'll handle replies programmatically)
7. Disable "Greeting messages" (optional)

### Rich Menu (Optional)

You can create a rich menu for better user experience:

1. Go to "Rich menus" in the console
2. Create a new rich menu with the following structure:

```json
{
  "size": {
    "width": 2500,
    "height": 1686
  },
  "selected": false,
  "name": "SMS Gateway Menu",
  "chatBarText": "Menu",
  "areas": [
    {
      "bounds": {
        "x": 0,
        "y": 0,
        "width": 1250,
        "height": 843
      },
      "action": {
        "type": "postback",
        "data": "{\"type\":\"send_sms\"}"
      }
    },
    {
      "bounds": {
        "x": 1250,
        "y": 0,
        "width": 1250,
        "height": 843
      },
      "action": {
        "type": "postback",
        "data": "{\"type\":\"check_status\"}"
      }
    },
    {
      "bounds": {
        "x": 0,
        "y": 843,
        "width": 1250,
        "height": 843
      },
      "action": {
        "type": "postback",
        "data": "{\"type\":\"view_messages\"}"
      }
    },
    {
      "bounds": {
        "x": 1250,
        "y": 843,
        "width": 1250,
        "height": 843
      },
      "action": {
        "type": "postback",
        "data": "{\"type\":\"settings\"}"
      }
    }
  ]
}
```

## Step 3: Configure Environment Variables

Update your `.env` file with the LINE Bot credentials:

```env
# LINE Bot Configuration
LINE_CHANNEL_ACCESS_TOKEN=your_channel_access_token_here
LINE_CHANNEL_SECRET=your_channel_secret_here
LINE_WEBHOOK_PATH=/webhook/line
LINE_ADMIN_USER_ID=your_line_user_id_here
LINE_QUICK_REPLY_ENABLED=true
LINE_RICH_MENU_ENABLED=true
```

To get your LINE User ID:
1. Add your bot as a friend
2. Send a message to the bot
3. Check the application logs for the user ID

## Step 4: Test Webhook Connection

1. Make sure your application is running and accessible via HTTPS
2. In the LINE Developers Console, go to "Messaging API" tab
3. Click "Verify" next to the webhook URL
4. You should see a success message

If verification fails:
- Check that your server is running
- Ensure the webhook URL is accessible via HTTPS
- Verify the webhook signature validation is working

## Step 5: Add Bot as Friend

1. Go to the "Messaging API" tab in LINE Developers Console
2. Find the QR code or Bot ID
3. Add the bot as a friend using LINE app
4. Send a test message to verify it's working

## Available Commands

Once set up, users can interact with the bot using these commands:

### Text Commands

- `/send +1234567890 Hello world!` - Send SMS message
- `/status` - Check recent message status
- `/messages` - View recent messages
- `/help` or `/menu` - Show available commands

### Quick Actions

The bot provides quick action buttons for:
- 📱 Send SMS
- 📊 Check Status
- 📋 View Messages
- ⚙️ Settings

## Example Interactions

### Sending SMS

**User:** `/send +1234567890 Hello from LINE Bot!`

**Bot Response:**
```
📱 Sending SMS to +1234567890...

Message: Hello from LINE Bot!
```

**Follow-up notification:**
```
✅ SMS Sent Successfully!

To: +1234567890
Message ID: 550e8400-e29b-41d4-a716-446655440000
Status: Sent

You'll receive updates when the message is delivered.
```

### Checking Status

**User:** `/status`

**Bot Response:**
```
📊 Recent SMS Messages

✅ +1234567890
   Hello from LINE Bot!
   2024-01-01 12:00:00

📤 +0987654321
   Another test message
   2024-01-01 11:30:00

Total: 15 messages
```

### Viewing Messages

**User:** `/messages`

**Bot Response:**
```
📋 Recent Messages

✅ To: +1234567890
   "Hello from LINE Bot!"
   2024-01-01 12:00:00
   Status: Delivered

📤 To: +0987654321
   "Another test message"
   2024-01-01 11:30:00
   Status: Sent

Showing 10 of 15 messages
```

## Admin Features

If you set `LINE_ADMIN_USER_ID` in your environment variables, that user will receive:

- Notifications when new SMS messages are received
- System alerts and errors
- Delivery status updates for all messages

### Admin Notifications

When an SMS is received:
```
📨 New SMS Received

From: +1234567890
Device: device-123
Time: 2024-01-01 12:00:00

Message:
Hello, this is an incoming SMS message.
```

## Troubleshooting

### Bot Not Responding

1. **Check webhook URL**: Ensure it's accessible via HTTPS
2. **Verify credentials**: Double-check Channel Access Token and Secret
3. **Check logs**: Look for error messages in application logs
4. **Test webhook**: Use the verify button in LINE Developers Console

### Webhook Verification Failed

1. **HTTPS required**: LINE webhooks require HTTPS
2. **Signature validation**: Ensure webhook signature validation is working
3. **Response format**: Webhook must return 200 status code
4. **Timeout**: Webhook must respond within 30 seconds

### Messages Not Sending

1. **SMS Gateway connection**: Check SMS Gateway API credentials
2. **Phone number format**: Ensure phone numbers are in E.164 format (+1234567890)
3. **Rate limits**: Check if you've hit SMS sending limits
4. **Device status**: Ensure SMS Gateway device is online

### Common Error Messages

**"Invalid signature"**
- Check LINE_CHANNEL_SECRET in environment variables
- Ensure webhook signature validation is implemented correctly

**"Channel access token is invalid"**
- Check LINE_CHANNEL_ACCESS_TOKEN in environment variables
- Regenerate token if necessary

**"Webhook URL is not valid"**
- Ensure URL is accessible via HTTPS
- Check that the endpoint returns 200 status code

## Security Considerations

1. **Keep credentials secure**: Never commit tokens to version control
2. **Use HTTPS**: Always use HTTPS for webhook URLs
3. **Validate signatures**: Always validate webhook signatures
4. **Rate limiting**: Implement rate limiting to prevent abuse
5. **Input validation**: Validate all user inputs

## Advanced Features

### Custom Rich Menu

Create a custom rich menu image (2500x1686 pixels) with sections for:
- Send SMS
- Check Status
- View Messages
- Settings

### Flex Messages

Use LINE's Flex Message format for rich, interactive messages:

```javascript
const flexMessage = {
  type: 'flex',
  altText: 'SMS Status',
  contents: {
    type: 'bubble',
    header: {
      type: 'box',
      layout: 'vertical',
      contents: [
        {
          type: 'text',
          text: 'SMS Status',
          weight: 'bold',
          size: 'lg'
        }
      ]
    },
    body: {
      type: 'box',
      layout: 'vertical',
      contents: [
        {
          type: 'text',
          text: `To: ${phoneNumber}`,
          size: 'sm'
        },
        {
          type: 'text',
          text: `Status: ${status}`,
          size: 'sm'
        }
      ]
    }
  }
};
```

### Push Notifications

Send proactive notifications to users:

```javascript
// Notify user when SMS is delivered
await lineService.pushMessage(userId, {
  type: 'text',
  text: `✅ Your SMS to ${phoneNumber} was delivered!`
});
```

## Support

For LINE Bot specific issues:
- [LINE Developers Documentation](https://developers.line.biz/en/docs/)
- [LINE Bot SDK for Node.js](https://github.com/line/line-bot-sdk-nodejs)
- [LINE Developers Community](https://www.line-community.me/)

For SMS Gateway API issues:
- Check the main README.md
- Review API documentation
- Check application logs
