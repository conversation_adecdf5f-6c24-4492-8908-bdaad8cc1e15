"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const smsGatewayClient_1 = __importDefault(require("./smsGatewayClient"));
const messageModel_1 = __importDefault(require("../models/messageModel"));
const webhookLogModel_1 = __importDefault(require("../models/webhookLogModel"));
const lineService_1 = __importDefault(require("./lineService"));
const types_1 = require("../types");
const config_1 = __importDefault(require("../config"));
const logger_1 = __importDefault(require("../utils/logger"));
const errorHandler_1 = require("../middleware/errorHandler");
class WebhookService {
    async registerSMSWebhook() {
        try {
            const webhookUrl = `${config_1.default.server.baseUrl}${config_1.default.webhooks.smsPath}`;
            logger_1.default.info('Registering SMS webhook', { webhookUrl });
            const webhook = await smsGatewayClient_1.default.registerWebhook({
                url: webhookUrl,
                event: types_1.WebHookEventType.SmsReceived,
            });
            logger_1.default.info('SMS webhook registered successfully', {
                webhookId: webhook.id,
                url: webhook.url,
                event: webhook.event,
            });
            return webhook;
        }
        catch (error) {
            logger_1.default.error('Failed to register SMS webhook', {
                error: error.message,
            });
            throw new errorHandler_1.AppError(`Failed to register SMS webhook: ${error.message}`, 500);
        }
    }
    async getWebhooks() {
        try {
            logger_1.default.debug('Getting registered webhooks');
            const webhooks = await smsGatewayClient_1.default.getWebhooks();
            logger_1.default.debug('Webhooks retrieved', { count: webhooks.length });
            return webhooks;
        }
        catch (error) {
            logger_1.default.error('Failed to get webhooks', {
                error: error.message,
            });
            throw new errorHandler_1.AppError(`Failed to get webhooks: ${error.message}`, 500);
        }
    }
    async deleteWebhook(webhookId) {
        try {
            logger_1.default.info('Deleting webhook', { webhookId });
            await smsGatewayClient_1.default.deleteWebhook(webhookId);
            logger_1.default.info('Webhook deleted successfully', { webhookId });
        }
        catch (error) {
            logger_1.default.error('Failed to delete webhook', {
                error: error.message,
                webhookId,
            });
            throw new errorHandler_1.AppError(`Failed to delete webhook: ${error.message}`, 500);
        }
    }
    async handleSMSWebhook(payload) {
        try {
            logger_1.default.info('Processing SMS webhook', {
                event: payload.event,
                messageId: payload.messageId,
                deviceId: payload.deviceId,
                phoneNumber: payload.phoneNumber,
            });
            const webhookLog = await webhookLogModel_1.default.create({
                type: 'sms',
                payload,
            });
            switch (payload.event) {
                case 'sms:received':
                    await this.handleSMSReceived(payload);
                    break;
                case 'sms:sent':
                    await this.handleSMSSent(payload);
                    break;
                case 'sms:delivered':
                    await this.handleSMSDelivered(payload);
                    break;
                case 'sms:failed':
                    await this.handleSMSFailed(payload);
                    break;
                default:
                    logger_1.default.warn('Unknown SMS webhook event', { event: payload.event });
            }
            await webhookLogModel_1.default.markAsProcessed(webhookLog.id);
            logger_1.default.info('SMS webhook processed successfully', {
                event: payload.event,
                webhookLogId: webhookLog.id,
            });
        }
        catch (error) {
            logger_1.default.error('Failed to process SMS webhook', {
                error: error.message,
                payload,
            });
            throw error;
        }
    }
    async handleSMSReceived(payload) {
        try {
            logger_1.default.info('Processing SMS received event', {
                deviceId: payload.deviceId,
                phoneNumber: payload.phoneNumber,
                message: payload.message?.substring(0, 100),
            });
            const message = {
                type: 'text',
                text: `📨 New SMS Received

From: ${payload.phoneNumber}
Device: ${payload.deviceId}
Time: ${new Date(payload.timestamp).toLocaleString()}

Message:
${payload.message}`,
            };
            await lineService_1.default.sendToAdmins(message);
            logger_1.default.info('SMS received notification sent to admins');
        }
        catch (error) {
            logger_1.default.error('Failed to handle SMS received event', {
                error: error.message,
                payload,
            });
        }
    }
    async handleSMSSent(payload) {
        try {
            if (!payload.messageId) {
                logger_1.default.warn('SMS sent event without messageId', { payload });
                return;
            }
            logger_1.default.info('Processing SMS sent event', {
                messageId: payload.messageId,
                phoneNumber: payload.phoneNumber,
            });
            await messageModel_1.default.update(payload.messageId, {
                status: types_1.ProcessState.Sent,
            });
            if (payload.phoneNumber) {
                await messageModel_1.default.updateRecipientStatus(payload.messageId, payload.phoneNumber, types_1.ProcessState.Sent);
            }
            const messageDetails = await messageModel_1.default.findById(payload.messageId);
            if (messageDetails?.lineUserId) {
                const statusMessage = {
                    type: 'text',
                    text: `📤 SMS Sent Successfully

To: ${payload.phoneNumber}
Message ID: ${payload.messageId}
Time: ${new Date(payload.timestamp).toLocaleString()}

Status: Sent ✅`,
                };
                await lineService_1.default.pushMessage(messageDetails.lineUserId, statusMessage);
            }
            logger_1.default.info('SMS sent event processed successfully');
        }
        catch (error) {
            logger_1.default.error('Failed to handle SMS sent event', {
                error: error.message,
                payload,
            });
        }
    }
    async handleSMSDelivered(payload) {
        try {
            if (!payload.messageId) {
                logger_1.default.warn('SMS delivered event without messageId', { payload });
                return;
            }
            logger_1.default.info('Processing SMS delivered event', {
                messageId: payload.messageId,
                phoneNumber: payload.phoneNumber,
            });
            await messageModel_1.default.update(payload.messageId, {
                status: types_1.ProcessState.Delivered,
                deliveredAt: new Date(payload.timestamp),
            });
            if (payload.phoneNumber) {
                await messageModel_1.default.updateRecipientStatus(payload.messageId, payload.phoneNumber, types_1.ProcessState.Delivered);
            }
            const messageDetails = await messageModel_1.default.findById(payload.messageId);
            if (messageDetails?.lineUserId) {
                const statusMessage = {
                    type: 'text',
                    text: `✅ SMS Delivered Successfully

To: ${payload.phoneNumber}
Message ID: ${payload.messageId}
Delivered: ${new Date(payload.timestamp).toLocaleString()}

Status: Delivered ✅`,
                };
                await lineService_1.default.pushMessage(messageDetails.lineUserId, statusMessage);
            }
            logger_1.default.info('SMS delivered event processed successfully');
        }
        catch (error) {
            logger_1.default.error('Failed to handle SMS delivered event', {
                error: error.message,
                payload,
            });
        }
    }
    async handleSMSFailed(payload) {
        try {
            if (!payload.messageId) {
                logger_1.default.warn('SMS failed event without messageId', { payload });
                return;
            }
            logger_1.default.info('Processing SMS failed event', {
                messageId: payload.messageId,
                phoneNumber: payload.phoneNumber,
                error: payload.error,
            });
            await messageModel_1.default.update(payload.messageId, {
                status: types_1.ProcessState.Failed,
                failedReason: payload.error || 'Unknown error',
            });
            if (payload.phoneNumber) {
                await messageModel_1.default.updateRecipientStatus(payload.messageId, payload.phoneNumber, types_1.ProcessState.Failed, payload.error);
            }
            const messageDetails = await messageModel_1.default.findById(payload.messageId);
            if (messageDetails?.lineUserId) {
                const statusMessage = {
                    type: 'text',
                    text: `❌ SMS Failed to Send

To: ${payload.phoneNumber}
Message ID: ${payload.messageId}
Time: ${new Date(payload.timestamp).toLocaleString()}

Error: ${payload.error || 'Unknown error'}

Status: Failed ❌`,
                };
                await lineService_1.default.pushMessage(messageDetails.lineUserId, statusMessage);
            }
            logger_1.default.info('SMS failed event processed successfully');
        }
        catch (error) {
            logger_1.default.error('Failed to handle SMS failed event', {
                error: error.message,
                payload,
            });
        }
    }
    async setupWebhooks() {
        try {
            logger_1.default.info('Setting up webhooks');
            const existingWebhooks = await this.getWebhooks();
            const webhookUrl = `${config_1.default.server.baseUrl}${config_1.default.webhooks.smsPath}`;
            const existingWebhook = existingWebhooks.find(w => w.url === webhookUrl);
            if (existingWebhook) {
                logger_1.default.info('SMS webhook already registered', {
                    webhookId: existingWebhook.id,
                    url: existingWebhook.url,
                });
            }
            else {
                await this.registerSMSWebhook();
            }
            logger_1.default.info('Webhooks setup completed');
        }
        catch (error) {
            logger_1.default.error('Failed to setup webhooks', {
                error: error.message,
            });
        }
    }
}
exports.default = new WebhookService();
//# sourceMappingURL=webhookService.js.map