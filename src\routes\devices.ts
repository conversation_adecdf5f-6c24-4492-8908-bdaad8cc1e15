import { Router } from 'express';
import deviceController from '../controllers/deviceController';

const router = Router();

/**
 * @swagger
 * /api/devices:
 *   get:
 *     tags: [Devices]
 *     summary: Get all devices
 *     description: Retrieve all registered SMS gateway devices
 *     responses:
 *       200:
 *         description: Devices retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Device'
 */
router.get('/', deviceController.getDevices);

/**
 * @swagger
 * /api/devices/settings:
 *   get:
 *     tags: [Devices]
 *     summary: Get device settings
 *     description: Retrieve current device settings configuration
 *     responses:
 *       200:
 *         description: Device settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/DeviceSettings'
 */
router.get('/settings', deviceController.getSettings);

/**
 * @swagger
 * /api/devices/settings:
 *   put:
 *     tags: [Devices]
 *     summary: Update device settings (full update)
 *     description: Update all device settings with new configuration
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/DeviceSettings'
 *           example:
 *             messages:
 *               limitPeriod: "PerDay"
 *               limitValue: 100
 *             webhooks:
 *               internetRequired: true
 *               retryCount: 3
 *             gateway:
 *               name: "My SMS Gateway"
 *     responses:
 *       200:
 *         description: Device settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *   patch:
 *     tags: [Devices]
 *     summary: Partially update device settings
 *     description: Update specific device settings fields
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/DeviceSettings'
 *           example:
 *             messages:
 *               limitValue: 200
 *     responses:
 *       200:
 *         description: Device settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
router.put('/settings', deviceController.updateSettings);
router.patch('/settings', deviceController.patchSettings);

/**
 * @route   GET /api/devices/health
 * @desc    Get system health
 * @access  Public
 */
router.get('/health', deviceController.getHealth);

/**
 * @route   GET /api/devices/logs
 * @desc    Get system logs
 * @access  Public
 */
router.get('/logs', deviceController.getLogs);

/**
 * @route   GET /api/devices/stats
 * @desc    Get device statistics
 * @access  Public
 */
router.get('/stats', deviceController.getDeviceStats);

/**
 * @route   POST /api/devices/test-connection
 * @desc    Test device connection
 * @access  Public
 */
router.post('/test-connection', deviceController.testConnection);

/**
 * @route   GET /api/devices/:deviceId
 * @desc    Get device by ID
 * @access  Public
 */
router.get('/:deviceId', deviceController.getDevice);

/**
 * @route   DELETE /api/devices/:deviceId
 * @desc    Delete device
 * @access  Public
 */
router.delete('/:deviceId', deviceController.deleteDevice);

/**
 * @route   POST /api/devices/:deviceId/export-inbox
 * @desc    Export inbox messages
 * @access  Public
 */
router.post('/:deviceId/export-inbox', deviceController.exportInbox);

export default router;
