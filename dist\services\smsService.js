"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const smsGatewayClient_1 = __importDefault(require("./smsGatewayClient"));
const messageModel_1 = __importDefault(require("../models/messageModel"));
const types_1 = require("../types");
const logger_1 = __importDefault(require("../utils/logger"));
const errorHandler_1 = require("../middleware/errorHandler");
class SMSService {
    async sendMessage(request, lineUserId) {
        try {
            logger_1.default.info('Sending SMS message', {
                phoneNumbers: request.phoneNumbers,
                messageLength: request.message.length,
                lineUserId,
            });
            const storedMessage = await messageModel_1.default.create({
                phoneNumbers: request.phoneNumbers,
                message: request.message,
                lineUserId,
                ttl: request.ttl,
                simNumber: request.simNumber,
                withDeliveryReport: request.withDeliveryReport,
            });
            const smsMessage = {
                id: storedMessage.id,
                message: request.message,
                phoneNumbers: request.phoneNumbers,
                ttl: request.ttl ?? null,
                simNumber: request.simNumber ?? null,
                withDeliveryReport: request.withDeliveryReport ?? null,
            };
            const messageState = await smsGatewayClient_1.default.sendMessage(smsMessage, request.skipPhoneValidation !== undefined
                ? { skipPhoneValidation: request.skipPhoneValidation }
                : {});
            await messageModel_1.default.update(storedMessage.id, {
                status: messageState.state,
            });
            for (const recipient of messageState.recipients) {
                await messageModel_1.default.updateRecipientStatus(storedMessage.id, recipient.phoneNumber, recipient.state, recipient.error);
            }
            logger_1.default.info('SMS message sent successfully', {
                messageId: storedMessage.id,
                status: messageState.state,
                recipientCount: messageState.recipients.length,
            });
            return {
                messageId: storedMessage.id,
                status: messageState.state,
                recipients: messageState.recipients,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to send SMS message', {
                error: error.message,
                phoneNumbers: request.phoneNumbers,
                lineUserId,
            });
            throw new errorHandler_1.AppError(`Failed to send SMS: ${error.message}`, 500);
        }
    }
    async getMessageStatus(messageId) {
        try {
            logger_1.default.debug('Getting message status', { messageId });
            const storedMessage = await messageModel_1.default.findById(messageId);
            if (!storedMessage) {
                throw new errorHandler_1.AppError('Message not found', 404);
            }
            const messageState = await smsGatewayClient_1.default.getMessageState(messageId);
            if (messageState.state !== storedMessage.status) {
                await messageModel_1.default.update(messageId, {
                    status: messageState.state,
                    ...(messageState.state === types_1.ProcessState.Delivered && {
                        deliveredAt: new Date(),
                    }),
                });
                for (const recipient of messageState.recipients) {
                    await messageModel_1.default.updateRecipientStatus(messageId, recipient.phoneNumber, recipient.state, recipient.error);
                }
            }
            logger_1.default.debug('Message status retrieved', {
                messageId,
                status: messageState.state,
                recipientCount: messageState.recipients.length,
            });
            return messageState;
        }
        catch (error) {
            logger_1.default.error('Failed to get message status', {
                error: error.message,
                messageId,
            });
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            throw new errorHandler_1.AppError(`Failed to get message status: ${error.message}`, 500);
        }
    }
    async getMessages(filter = {}, page = 1, limit = 20) {
        try {
            logger_1.default.debug('Getting messages', { filter, page, limit });
            const { messages, total } = await messageModel_1.default.findMany(filter, { page, limit });
            const totalPages = Math.ceil(total / limit);
            logger_1.default.debug('Messages retrieved', {
                count: messages.length,
                total,
                page,
                totalPages,
            });
            return {
                success: true,
                data: messages,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                },
            };
        }
        catch (error) {
            logger_1.default.error('Failed to get messages', {
                error: error.message,
                filter,
                page,
                limit,
            });
            throw new errorHandler_1.AppError(`Failed to get messages: ${error.message}`, 500);
        }
    }
    async getMessageDetails(messageId) {
        try {
            logger_1.default.debug('Getting message details', { messageId });
            const message = await messageModel_1.default.findById(messageId);
            if (!message) {
                throw new errorHandler_1.AppError('Message not found', 404);
            }
            const recipients = await messageModel_1.default.getRecipients(messageId);
            logger_1.default.debug('Message details retrieved', {
                messageId,
                recipientCount: recipients.length,
            });
            return {
                message,
                recipients,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to get message details', {
                error: error.message,
                messageId,
            });
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            throw new errorHandler_1.AppError(`Failed to get message details: ${error.message}`, 500);
        }
    }
    async refreshMessageStatus(messageId) {
        try {
            logger_1.default.info('Refreshing message status', { messageId });
            const messageState = await smsGatewayClient_1.default.getMessageState(messageId);
            await messageModel_1.default.update(messageId, {
                status: messageState.state,
                ...(messageState.state === types_1.ProcessState.Delivered && {
                    deliveredAt: new Date(),
                }),
                ...(messageState.state === types_1.ProcessState.Failed && {
                    failedReason: 'Message failed to send',
                }),
            });
            for (const recipient of messageState.recipients) {
                await messageModel_1.default.updateRecipientStatus(messageId, recipient.phoneNumber, recipient.state, recipient.error);
            }
            logger_1.default.info('Message status refreshed', {
                messageId,
                status: messageState.state,
            });
            return messageState;
        }
        catch (error) {
            logger_1.default.error('Failed to refresh message status', {
                error: error.message,
                messageId,
            });
            throw new errorHandler_1.AppError(`Failed to refresh message status: ${error.message}`, 500);
        }
    }
    async getMessageStats(dateFrom, dateTo) {
        try {
            logger_1.default.debug('Getting message statistics', { dateFrom, dateTo });
            const filter = {};
            if (dateFrom)
                filter.dateFrom = dateFrom;
            if (dateTo)
                filter.dateTo = dateTo;
            const { messages } = await messageModel_1.default.findMany(filter, { page: 1, limit: 10000 });
            const stats = {
                total: messages.length,
                pending: messages.filter(m => m.status === types_1.ProcessState.Pending).length,
                sent: messages.filter(m => m.status === types_1.ProcessState.Sent).length,
                delivered: messages.filter(m => m.status === types_1.ProcessState.Delivered).length,
                failed: messages.filter(m => m.status === types_1.ProcessState.Failed).length,
                byDate: [],
            };
            const dateGroups = messages.reduce((acc, message) => {
                const date = message.createdAt.toISOString().split('T')[0];
                acc[date] = (acc[date] || 0) + 1;
                return acc;
            }, {});
            stats.byDate = Object.entries(dateGroups)
                .map(([date, count]) => ({ date, count }))
                .sort((a, b) => a.date.localeCompare(b.date));
            logger_1.default.debug('Message statistics calculated', stats);
            return stats;
        }
        catch (error) {
            logger_1.default.error('Failed to get message statistics', {
                error: error.message,
                dateFrom,
                dateTo,
            });
            throw new errorHandler_1.AppError(`Failed to get message statistics: ${error.message}`, 500);
        }
    }
}
exports.default = new SMSService();
//# sourceMappingURL=smsService.js.map