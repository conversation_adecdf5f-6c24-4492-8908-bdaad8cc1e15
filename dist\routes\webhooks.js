"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const webhookController_1 = __importDefault(require("../controllers/webhookController"));
const validation_1 = require("../middleware/validation");
const config_1 = __importDefault(require("../config"));
const router = (0, express_1.Router)();
router.post('/sms', (0, validation_1.validateWebhookSignature)(config_1.default.webhooks.smsSecret), webhookController_1.default.handleSMSWebhook);
router.post('/sms/test', webhookController_1.default.testWebhook);
router.get('/health', webhookController_1.default.healthCheck);
router.get('/', webhookController_1.default.getWebhooks);
router.post('/register', webhookController_1.default.registerWebhook);
router.delete('/:webhookId', webhookController_1.default.deleteWebhook);
exports.default = router;
//# sourceMappingURL=webhooks.js.map