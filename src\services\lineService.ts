import { Client, ClientConfig, WebhookEvent, MessageEvent, TextMessage, FlexMessage, QuickReply, QuickReplyItem } from '@line/bot-sdk';
import config from '../config';
import lineUserModel from '../models/lineUserModel';
import { LineUser, LineQuickAction } from '../types';
import logger from '../utils/logger';
import { AppError } from '../middleware/errorHandler';

class LineService {
  private client: Client;

  constructor() {
    const clientConfig: ClientConfig = {
      channelAccessToken: config.lineBot.channelAccessToken,
      channelSecret: config.lineBot.channelSecret,
    };

    this.client = new Client(clientConfig);

    logger.info('LINE Bot client initialized', {
      channelAccessToken: config.lineBot.channelAccessToken ? 'configured' : 'missing',
      channelSecret: config.lineBot.channelSecret ? 'configured' : 'missing',
    });
  }

  /**
   * Handle webhook events
   */
  async handleWebhookEvents(events: WebhookEvent[]): Promise<void> {
    try {
      logger.info('Processing LINE webhook events', { count: events.length });

      for (const event of events) {
        await this.handleEvent(event);
      }

      logger.info('LINE webhook events processed successfully');
    } catch (error) {
      logger.error('Failed to handle LINE webhook events', {
        error: (error as Error).message,
        eventsCount: events.length,
      });
      throw error;
    }
  }

  /**
   * Handle individual webhook event
   */
  private async handleEvent(event: WebhookEvent): Promise<void> {
    try {
      logger.debug('Processing LINE event', { type: event.type, source: event.source });

      // Handle user follow/unfollow events
      if (event.type === 'follow' || event.type === 'unfollow') {
        await this.handleUserEvent(event);
        return;
      }

      // Handle message events
      if (event.type === 'message') {
        await this.handleMessageEvent(event as MessageEvent);
        return;
      }

      // Handle postback events (quick actions)
      if (event.type === 'postback') {
        await this.handlePostbackEvent(event);
        return;
      }

      logger.debug('Unhandled LINE event type', { type: event.type });
    } catch (error) {
      logger.error('Failed to handle LINE event', {
        error: (error as Error).message,
        event,
      });
    }
  }

  /**
   * Handle user follow/unfollow events
   */
  private async handleUserEvent(event: any): Promise<void> {
    const userId = event.source.userId;
    
    if (!userId) {
      logger.warn('User event without userId', { event });
      return;
    }

    try {
      if (event.type === 'follow') {
        // Get user profile
        const profile = await this.client.getProfile(userId);
        
        // Create or update user in database
        await lineUserModel.upsert({
          id: userId,
          displayName: profile.displayName,
          pictureUrl: profile.pictureUrl,
          statusMessage: profile.statusMessage,
          isAdmin: userId === config.lineBot.adminUserId,
        });

        // Send welcome message
        await this.sendWelcomeMessage(userId);

        logger.info('User followed bot', { userId, displayName: profile.displayName });
      } else if (event.type === 'unfollow') {
        logger.info('User unfollowed bot', { userId });
      }
    } catch (error) {
      logger.error('Failed to handle user event', {
        error: (error as Error).message,
        userId,
        eventType: event.type,
      });
    }
  }

  /**
   * Handle message events
   */
  private async handleMessageEvent(event: MessageEvent): Promise<void> {
    const userId = event.source.userId;
    
    if (!userId) {
      logger.warn('Message event without userId', { event });
      return;
    }

    if (event.message.type !== 'text') {
      await this.replyMessage(event.replyToken, {
        type: 'text',
        text: 'Sorry, I can only handle text messages at the moment.',
      });
      return;
    }

    const textMessage = event.message as TextMessage;
    const messageText = textMessage.text.toLowerCase().trim();

    logger.info('Received LINE message', {
      userId,
      message: messageText,
      replyToken: event.replyToken,
    });

    // Handle different commands
    if (messageText.startsWith('/send')) {
      await this.handleSendSMSCommand(event.replyToken, userId, messageText);
    } else if (messageText === '/status' || messageText === '/messages') {
      await this.handleStatusCommand(event.replyToken, userId);
    } else if (messageText === '/help' || messageText === '/menu') {
      await this.sendMainMenu(event.replyToken);
    } else {
      await this.sendMainMenu(event.replyToken);
    }
  }

  /**
   * Handle postback events (quick actions)
   */
  private async handlePostbackEvent(event: any): Promise<void> {
    const userId = event.source.userId;
    const data = event.postback.data;

    logger.info('Received LINE postback', { userId, data });

    try {
      const action: LineQuickAction = JSON.parse(data);

      switch (action.type) {
        case 'send_sms':
          await this.handleSendSMSAction(event.replyToken, userId, action.data);
          break;
        case 'check_status':
          await this.handleStatusCommand(event.replyToken, userId);
          break;
        case 'view_messages':
          await this.handleViewMessagesAction(event.replyToken, userId);
          break;
        case 'settings':
          await this.handleSettingsAction(event.replyToken, userId);
          break;
        default:
          await this.sendMainMenu(event.replyToken);
      }
    } catch (error) {
      logger.error('Failed to handle postback event', {
        error: (error as Error).message,
        userId,
        data,
      });

      await this.replyMessage(event.replyToken, {
        type: 'text',
        text: 'Sorry, something went wrong processing your request.',
      });
    }
  }

  /**
   * Send welcome message to new users
   */
  private async sendWelcomeMessage(userId: string): Promise<void> {
    const welcomeMessage = {
      type: 'text' as const,
      text: `Welcome to SMS Gateway Bot! 🎉

I can help you send SMS messages and manage your SMS gateway. Here are some things you can do:

• Send SMS messages
• Check message status
• View recent messages
• Manage settings

Type /help or /menu to see all available options.`,
    };

    await this.pushMessage(userId, welcomeMessage);
  }

  /**
   * Send main menu
   */
  private async sendMainMenu(replyToken: string): Promise<void> {
    const quickReply: QuickReply = {
      items: [
        {
          type: 'action',
          action: {
            type: 'postback',
            label: '📱 Send SMS',
            data: JSON.stringify({ type: 'send_sms' }),
          },
        },
        {
          type: 'action',
          action: {
            type: 'postback',
            label: '📊 Check Status',
            data: JSON.stringify({ type: 'check_status' }),
          },
        },
        {
          type: 'action',
          action: {
            type: 'postback',
            label: '📋 View Messages',
            data: JSON.stringify({ type: 'view_messages' }),
          },
        },
        {
          type: 'action',
          action: {
            type: 'postback',
            label: '⚙️ Settings',
            data: JSON.stringify({ type: 'settings' }),
          },
        },
      ],
    };

    const message = {
      type: 'text' as const,
      text: `SMS Gateway Bot Menu 🤖

What would you like to do?

Commands:
• /send [phone] [message] - Send SMS
• /status - Check recent messages
• /help - Show this menu`,
      quickReply,
    };

    await this.replyMessage(replyToken, message);
  }

  /**
   * Handle send SMS command
   */
  private async handleSendSMSCommand(replyToken: string, userId: string, messageText: string): Promise<void> {
    // Parse command: /send +1234567890 Hello world
    const parts = messageText.split(' ');
    
    if (parts.length < 3) {
      await this.replyMessage(replyToken, {
        type: 'text',
        text: 'Usage: /send [phone_number] [message]\nExample: /send +1234567890 Hello world!',
      });
      return;
    }

    const phoneNumber = parts[1]!;
    const message = parts.slice(2).join(' ');

    // Validate phone number format
    if (!phoneNumber.startsWith('+')) {
      await this.replyMessage(replyToken, {
        type: 'text',
        text: 'Please provide a valid phone number starting with + (e.g., +1234567890)',
      });
      return;
    }

    await this.replyMessage(replyToken, {
      type: 'text',
      text: `📱 Sending SMS to ${phoneNumber}...\n\nMessage: ${message}`,
    });

    // This will be implemented when we integrate with SMS service
    logger.info('SMS send request from LINE', { userId, phoneNumber, message });
  }

  /**
   * Handle status command
   */
  private async handleStatusCommand(replyToken: string, userId: string): Promise<void> {
    await this.replyMessage(replyToken, {
      type: 'text',
      text: '📊 Recent SMS Status\n\nThis feature will show your recent SMS messages and their delivery status.',
    });
  }

  /**
   * Handle quick actions
   */
  private async handleSendSMSAction(replyToken: string, userId: string, data: any): Promise<void> {
    await this.replyMessage(replyToken, {
      type: 'text',
      text: '📱 Send SMS\n\nTo send an SMS, use the command:\n/send [phone_number] [message]\n\nExample:\n/send +1234567890 Hello world!',
    });
  }

  private async handleViewMessagesAction(replyToken: string, userId: string): Promise<void> {
    await this.replyMessage(replyToken, {
      type: 'text',
      text: '📋 Recent Messages\n\nThis will show your recent SMS messages and their status.',
    });
  }

  private async handleSettingsAction(replyToken: string, userId: string): Promise<void> {
    await this.replyMessage(replyToken, {
      type: 'text',
      text: '⚙️ Settings\n\nSettings management will be available here.',
    });
  }

  /**
   * Send reply message
   */
  async replyMessage(replyToken: string, message: any): Promise<void> {
    try {
      await this.client.replyMessage(replyToken, message);
      logger.debug('Reply message sent', { replyToken });
    } catch (error) {
      logger.error('Failed to send reply message', {
        error: (error as Error).message,
        replyToken,
      });
      throw error;
    }
  }

  /**
   * Send push message
   */
  async pushMessage(userId: string, message: any): Promise<void> {
    try {
      await this.client.pushMessage(userId, message);
      logger.debug('Push message sent', { userId });
    } catch (error) {
      logger.error('Failed to send push message', {
        error: (error as Error).message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Broadcast message to all users
   */
  async broadcastMessage(message: any): Promise<void> {
    try {
      await this.client.broadcast(message);
      logger.info('Broadcast message sent');
    } catch (error) {
      logger.error('Failed to send broadcast message', {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Send message to admin users
   */
  async sendToAdmins(message: any): Promise<void> {
    try {
      const adminUsers = await lineUserModel.findAdmins();
      
      for (const admin of adminUsers) {
        await this.pushMessage(admin.id, message);
      }

      logger.info('Message sent to admins', { adminCount: adminUsers.length });
    } catch (error) {
      logger.error('Failed to send message to admins', {
        error: (error as Error).message,
      });
      throw error;
    }
  }
}

export default new LineService();
