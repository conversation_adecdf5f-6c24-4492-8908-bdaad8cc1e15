/**
 * Main types export file
 * Provides a centralized export for all type definitions
 */

// Base types
export * from './base/common';
export * from './base/api';
export * from './base/database';

// SMS types
export * from './sms/message';
export * from './sms/webhook';
export * from './sms/gateway';

// Device types
export * from './device/device';
export * from './device/settings';

// LINE types
export * from './line/user';
export * from './line/message';
export * from './line/webhook';

// Adapter types
export * from './adapters/sms-gateway';
export * from './adapters/line-bot';

// Legacy exports for backward compatibility (will be removed after migration)
// These re-export the same types with their original names to avoid breaking existing imports

// Legacy SMS types
export type { SMSMessage } from './sms/message';
export type { MessageState } from './sms/message';
export type { RecipientState } from './sms/message';
export type { StoredMessage } from './sms/message';
export type { MessageFilter } from './sms/message';
export type { SendSMSRequest, SendSMSResponse } from './sms/message';

// Legacy webhook types (export as values for enums)
export type { WebHook } from './sms/webhook';
export { WebHookEventType } from './sms/webhook';
export type { WebhookLog } from './sms/webhook';

// Legacy device types
export type { Device } from './device/device';
export type { DeviceSettings } from './device/settings';

// Legacy LINE types
export type { LineUser } from './line/user';
export type { LineMessage } from './line/message';
export type { LineQuickAction } from './line/message';

// Legacy base types (export as values for enums)
export { ProcessState } from './base/common';
export type { ApiResponse, PaginatedResponse } from './base/api';
export type { HttpClient } from './base/api';

// Legacy model types (from model files)
export type { CreateMessageData, UpdateMessageData } from './sms/message';
export type { CreateLineUserData, UpdateLineUserData } from './line/user';
export type { PaginationOptions } from './base/common';

// Request/Response types
export type { GetMessagesRequest, MessageStatusRequest } from './sms/message';
