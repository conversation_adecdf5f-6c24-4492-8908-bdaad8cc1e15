// SMS Gateway Types
export interface SMSMessage {
  id?: string | null;
  message: string;
  ttl?: number | null;
  phoneNumbers: string[];
  simNumber?: number | null;
  withDeliveryReport?: boolean | null;
}

export interface MessageState {
  id: string;
  state: ProcessState;
  recipients: RecipientState[];
}

export interface RecipientState {
  phoneNumber: string;
  state: ProcessState;
  error?: string | null;
}

export enum ProcessState {
  Pending = 'Pending',
  Processed = 'Processed',
  Sent = 'Sent',
  Delivered = 'Delivered',
  Failed = 'Failed',
}

export interface WebHook {
  id: string;
  event: WebHookEventType;
  url: string;
  deviceId: string;
}

export enum WebHookEventType {
  SmsReceived = 'sms:received',
  SmsSent = 'sms:sent',
  SmsDelivered = 'sms:delivered',
  SmsFailed = 'sms:failed',
}

export interface Device {
  id: string;
  name: string;
  createdAt: string;
  lastSeen: string;
  updatedAt: string;
  deletedAt?: string | null;
}

export interface DeviceSettings {
  messages?: SettingsMessages;
  webhooks?: SettingsWebhooks;
  gateway?: SettingsGateway;
  encryption?: SettingsEncryption;
  logs?: SettingsLogs;
  ping?: SettingsPing;
}

export interface SettingsMessages {
  limitPeriod?: 'PerMinute' | 'PerHour' | 'PerDay';
  limitValue?: number;
}

export interface SettingsWebhooks {
  internetRequired?: boolean;
  retryCount?: number;
}

export interface SettingsGateway {
  name?: string;
}

export interface SettingsEncryption {
  enabled?: boolean;
}

export interface SettingsLogs {
  ttl?: number;
}

export interface SettingsPing {
  enabled?: boolean;
  interval?: number;
}

// LINE Bot Types
export interface LineUser {
  id: string;
  displayName?: string;
  pictureUrl?: string;
  statusMessage?: string;
  isAdmin: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface LineMessage {
  id: string;
  userId: string;
  messageType: string;
  content: string;
  timestamp: Date;
  replyToken?: string;
}

// Database Models
export interface StoredMessage {
  id: string;
  phoneNumbers: string;
  message: string;
  status: ProcessState;
  createdAt: Date;
  updatedAt: Date;
  deliveredAt?: Date;
  failedReason?: string;
  lineUserId?: string;
}

export interface WebhookLog {
  id: string;
  type: 'sms' | 'line';
  payload: string;
  processed: boolean;
  createdAt: Date;
  processedAt?: Date;
  error?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// HTTP Client Interface for SMS Gateway
export interface HttpClient {
  get<T>(url: string, headers?: Record<string, string>): Promise<T>;
  post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  patch<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  delete<T>(url: string, headers?: Record<string, string>): Promise<T>;
}

// Request/Response interfaces
export interface SendSMSRequest {
  phoneNumbers: string[];
  message: string;
  ttl?: number;
  simNumber?: number;
  withDeliveryReport?: boolean;
  skipPhoneValidation?: boolean;
}

export interface SendSMSResponse {
  messageId: string;
  status: ProcessState;
  recipients: RecipientState[];
}

export interface MessageStatusRequest {
  messageId: string;
}

export interface GetMessagesRequest {
  page?: number;
  limit?: number;
  status?: ProcessState;
  phoneNumber?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface LineQuickAction {
  type: 'send_sms' | 'check_status' | 'view_messages' | 'settings';
  label: string;
  data?: any;
}

// Filter interfaces
export interface MessageFilter {
  status?: ProcessState;
  phoneNumber?: string;
  lineUserId?: string;
  dateFrom?: Date;
  dateTo?: Date;
}
