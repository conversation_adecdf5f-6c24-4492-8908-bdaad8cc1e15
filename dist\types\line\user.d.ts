import { BaseEntity } from '../base/common';
export interface LineUser extends BaseEntity {
    displayName?: string | undefined;
    pictureUrl?: string | undefined;
    statusMessage?: string | undefined;
    isAdmin: boolean;
}
export interface CreateLineUserData {
    id: string;
    displayName?: string | undefined;
    pictureUrl?: string | undefined;
    statusMessage?: string | undefined;
    isAdmin?: boolean | undefined;
}
export interface UpdateLineUserData {
    displayName?: string | undefined;
    pictureUrl?: string | undefined;
    statusMessage?: string | undefined;
    isAdmin?: boolean | undefined;
}
export interface LineUserFilter {
    displayName?: string;
    isAdmin?: boolean;
    createdAfter?: Date;
    createdBefore?: Date;
}
//# sourceMappingURL=user.d.ts.map