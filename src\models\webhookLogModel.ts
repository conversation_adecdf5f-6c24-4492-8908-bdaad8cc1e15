import { v4 as uuidv4 } from 'uuid';
import database from './database';
import { WebhookLog } from '../types';
import logger from '../utils/logger';

export interface CreateWebhookLogData {
  type: 'sms' | 'line';
  payload: any;
}

export interface UpdateWebhookLogData {
  processed?: boolean;
  error?: string;
}

export interface WebhookLogFilter {
  type?: 'sms' | 'line';
  processed?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

class WebhookLogModel {
  async create(data: CreateWebhookLogData): Promise<WebhookLog> {
    const id = uuidv4();
    const now = new Date();

    const log: WebhookLog = {
      id,
      type: data.type,
      payload: JSON.stringify(data.payload),
      processed: false,
      createdAt: now,
    };

    await database.run(
      `INSERT INTO webhook_logs (
        id, type, payload, processed, created_at
      ) VALUES (?, ?, ?, ?, ?)`,
      [
        log.id,
        log.type,
        log.payload,
        log.processed ? 1 : 0,
        log.createdAt.toISOString(),
      ]
    );

    logger.debug('Webhook log created in database', {
      logId: id,
      type: data.type,
      payloadSize: log.payload.length,
    });

    return log;
  }

  async findById(id: string): Promise<WebhookLog | null> {
    const row = await database.get<any>(
      'SELECT * FROM webhook_logs WHERE id = ?',
      [id]
    );

    if (!row) {
      return null;
    }

    return this.mapRowToLog(row);
  }

  async update(id: string, data: UpdateWebhookLogData): Promise<void> {
    const updates: string[] = [];
    const values: any[] = [];

    if (data.processed !== undefined) {
      updates.push('processed = ?');
      values.push(data.processed ? 1 : 0);

      if (data.processed) {
        updates.push('processed_at = ?');
        values.push(new Date().toISOString());
      }
    }

    if (data.error !== undefined) {
      updates.push('error = ?');
      values.push(data.error);
    }

    if (updates.length === 0) {
      return;
    }

    values.push(id);

    await database.run(
      `UPDATE webhook_logs SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    logger.debug('Webhook log updated in database', { logId: id, updates: data });
  }

  async markAsProcessed(id: string, error?: string): Promise<void> {
    await this.update(id, {
      processed: true,
      error,
    });
  }

  async findMany(
    filter: WebhookLogFilter = {},
    pagination: PaginationOptions = { page: 1, limit: 50 }
  ): Promise<{ logs: WebhookLog[]; total: number }> {
    const conditions: string[] = [];
    const values: any[] = [];

    if (filter.type) {
      conditions.push('type = ?');
      values.push(filter.type);
    }

    if (filter.processed !== undefined) {
      conditions.push('processed = ?');
      values.push(filter.processed ? 1 : 0);
    }

    if (filter.dateFrom) {
      conditions.push('created_at >= ?');
      values.push(filter.dateFrom.toISOString());
    }

    if (filter.dateTo) {
      conditions.push('created_at <= ?');
      values.push(filter.dateTo.toISOString());
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countResult = await database.get<{ count: number }>(
      `SELECT COUNT(*) as count FROM webhook_logs ${whereClause}`,
      values
    );
    const total = countResult?.count || 0;

    // Get paginated results
    const offset = (pagination.page - 1) * pagination.limit;
    const rows = await database.all<any>(
      `SELECT * FROM webhook_logs ${whereClause} 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [...values, pagination.limit, offset]
    );

    const logs = rows.map(row => this.mapRowToLog(row));

    return { logs, total };
  }

  async findUnprocessed(limit: number = 100): Promise<WebhookLog[]> {
    const rows = await database.all<any>(
      'SELECT * FROM webhook_logs WHERE processed = 0 ORDER BY created_at ASC LIMIT ?',
      [limit]
    );

    return rows.map(row => this.mapRowToLog(row));
  }

  async getStats(): Promise<{
    total: number;
    processed: number;
    unprocessed: number;
    errors: number;
    smsLogs: number;
    lineLogs: number;
  }> {
    const totalResult = await database.get<{ count: number }>(
      'SELECT COUNT(*) as count FROM webhook_logs'
    );

    const processedResult = await database.get<{ count: number }>(
      'SELECT COUNT(*) as count FROM webhook_logs WHERE processed = 1'
    );

    const unprocessedResult = await database.get<{ count: number }>(
      'SELECT COUNT(*) as count FROM webhook_logs WHERE processed = 0'
    );

    const errorsResult = await database.get<{ count: number }>(
      'SELECT COUNT(*) as count FROM webhook_logs WHERE error IS NOT NULL'
    );

    const smsResult = await database.get<{ count: number }>(
      'SELECT COUNT(*) as count FROM webhook_logs WHERE type = "sms"'
    );

    const lineResult = await database.get<{ count: number }>(
      'SELECT COUNT(*) as count FROM webhook_logs WHERE type = "line"'
    );

    return {
      total: totalResult?.count || 0,
      processed: processedResult?.count || 0,
      unprocessed: unprocessedResult?.count || 0,
      errors: errorsResult?.count || 0,
      smsLogs: smsResult?.count || 0,
      lineLogs: lineResult?.count || 0,
    };
  }

  async deleteOldLogs(olderThanDays: number = 7): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await database.run(
      'DELETE FROM webhook_logs WHERE created_at < ? AND processed = 1',
      [cutoffDate.toISOString()]
    );

    logger.info('Old webhook logs deleted', {
      deletedCount: result.changes,
      cutoffDate: cutoffDate.toISOString(),
    });

    return result.changes || 0;
  }

  private mapRowToLog(row: any): WebhookLog {
    return {
      id: row.id,
      type: row.type as 'sms' | 'line',
      payload: row.payload,
      processed: row.processed === 1,
      createdAt: new Date(row.created_at),
      processedAt: row.processed_at ? new Date(row.processed_at) : undefined,
      error: row.error || undefined,
    };
  }
}

export default new WebhookLogModel();
