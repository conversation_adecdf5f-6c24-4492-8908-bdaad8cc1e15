import smsGatewayClient from './smsGatewayClient';
import { Device, DeviceSettings, WebHook } from '../types';
import logger from '../utils/logger';
import { AppError } from '../middleware/errorHandler';

class DeviceService {
  /**
   * Get all devices
   */
  async getDevices(): Promise<Device[]> {
    try {
      logger.debug('Getting devices');

      const devices = await smsGatewayClient.getDevices();

      logger.debug('Devices retrieved', { count: devices.length });

      return devices;
    } catch (error) {
      logger.error('Failed to get devices', {
        error: (error as Error).message,
      });

      throw new AppError(
        `Failed to get devices: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get device by ID
   */
  async getDevice(deviceId: string): Promise<Device> {
    try {
      logger.debug('Getting device', { deviceId });

      const devices = await this.getDevices();
      const device = devices.find(d => d.id === deviceId);

      if (!device) {
        throw new AppError('Device not found', 404);
      }

      logger.debug('Device retrieved', { deviceId, deviceName: device.name });

      return device;
    } catch (error) {
      logger.error('Failed to get device', {
        error: (error as Error).message,
        deviceId,
      });

      if (error instanceof AppError) {
        throw error;
      }

      throw new AppError(
        `Failed to get device: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Delete device
   */
  async deleteDevice(deviceId: string): Promise<void> {
    try {
      logger.info('Deleting device', { deviceId });

      await smsGatewayClient.deleteDevice(deviceId);

      logger.info('Device deleted successfully', { deviceId });
    } catch (error) {
      logger.error('Failed to delete device', {
        error: (error as Error).message,
        deviceId,
      });

      throw new AppError(
        `Failed to delete device: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get device settings
   */
  async getSettings(): Promise<DeviceSettings> {
    try {
      logger.debug('Getting device settings');

      const settings = await smsGatewayClient.getSettings();

      logger.debug('Device settings retrieved');

      return settings;
    } catch (error) {
      logger.error('Failed to get device settings', {
        error: (error as Error).message,
      });

      throw new AppError(
        `Failed to get device settings: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Update device settings (full update)
   */
  async updateSettings(settings: DeviceSettings): Promise<void> {
    try {
      logger.info('Updating device settings', settings);

      await smsGatewayClient.updateSettings(settings);

      logger.info('Device settings updated successfully');
    } catch (error) {
      logger.error('Failed to update device settings', {
        error: (error as Error).message,
        settings,
      });

      throw new AppError(
        `Failed to update device settings: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Partially update device settings
   */
  async patchSettings(settings: Partial<DeviceSettings>): Promise<void> {
    try {
      logger.info('Partially updating device settings', settings);

      await smsGatewayClient.patchSettings(settings);

      logger.info('Device settings partially updated successfully');
    } catch (error) {
      logger.error('Failed to partially update device settings', {
        error: (error as Error).message,
        settings,
      });

      throw new AppError(
        `Failed to partially update device settings: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get system health
   */
  async getHealth(): Promise<any> {
    try {
      logger.debug('Getting system health');

      const health = await smsGatewayClient.getHealth();

      logger.debug('System health retrieved', { status: health.status });

      return health;
    } catch (error) {
      logger.error('Failed to get system health', {
        error: (error as Error).message,
      });

      throw new AppError(
        `Failed to get system health: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get system logs
   */
  async getLogs(from?: Date, to?: Date): Promise<any[]> {
    try {
      logger.debug('Getting system logs', { from, to });

      const logs = await smsGatewayClient.getLogs(from, to);

      logger.debug('System logs retrieved', { count: logs.length });

      return logs;
    } catch (error) {
      logger.error('Failed to get system logs', {
        error: (error as Error).message,
        from,
        to,
      });

      throw new AppError(
        `Failed to get system logs: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Export inbox messages
   */
  async exportInbox(deviceId: string, since: Date, until: Date): Promise<void> {
    try {
      logger.info('Exporting inbox messages', { deviceId, since, until });

      await smsGatewayClient.exportInbox({
        deviceId,
        since: since.toISOString(),
        until: until.toISOString(),
      });

      logger.info('Inbox export requested successfully', { deviceId, since, until });
    } catch (error) {
      logger.error('Failed to export inbox', {
        error: (error as Error).message,
        deviceId,
        since,
        until,
      });

      throw new AppError(
        `Failed to export inbox: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get device statistics
   */
  async getDeviceStats(): Promise<{
    totalDevices: number;
    activeDevices: number;
    recentlySeenDevices: number;
    devicesByStatus: Array<{ status: string; count: number }>;
  }> {
    try {
      logger.debug('Getting device statistics');

      const devices = await this.getDevices();
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const stats = {
        totalDevices: devices.length,
        activeDevices: devices.filter(d => !d.deletedAt).length,
        recentlySeenDevices: devices.filter(d => 
          new Date(d.lastSeen) > oneDayAgo
        ).length,
        devicesByStatus: [] as Array<{ status: string; count: number }>,
      };

      // Group devices by status
      const statusGroups = devices.reduce((acc, device) => {
        let status = 'offline';
        
        if (device.deletedAt) {
          status = 'deleted';
        } else if (new Date(device.lastSeen) > oneHourAgo) {
          status = 'online';
        } else if (new Date(device.lastSeen) > oneDayAgo) {
          status = 'recently_seen';
        }

        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      stats.devicesByStatus = Object.entries(statusGroups)
        .map(([status, count]) => ({ status, count }));

      logger.debug('Device statistics calculated', stats);

      return stats;
    } catch (error) {
      logger.error('Failed to get device statistics', {
        error: (error as Error).message,
      });

      throw new AppError(
        `Failed to get device statistics: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Test device connection
   */
  async testConnection(): Promise<boolean> {
    try {
      logger.info('Testing device connection');

      const isConnected = await smsGatewayClient.testConnection();

      logger.info('Device connection test completed', { isConnected });

      return isConnected;
    } catch (error) {
      logger.error('Device connection test failed', {
        error: (error as Error).message,
      });

      return false;
    }
  }
}

export default new DeviceService();
