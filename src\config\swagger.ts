import swaggerJsdoc from 'swagger-jsdoc';
import { SwaggerDefinition } from 'swagger-jsdoc';
import config from './index';

const swaggerDefinition: SwaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'SMS Gateway API with LINE Bot Integration',
    version: '1.0.0',
    description: `
A comprehensive Node.js API application that integrates with the Android SMS Gateway service and LINE Bot for seamless SMS management and real-time notifications.

## Features
- 📱 SMS Management: Send, track, and manage SMS messages
- 🤖 LINE Bot Integration: Control SMS operations directly from LINE chat
- 📊 Real-time Status Tracking: Monitor message delivery status with webhooks
- 🔧 Device Management: Configure and manage SMS gateway devices
- 📋 Message History: View and filter SMS message history
- 🔔 Instant Notifications: Receive SMS status updates in LINE

## Authentication
Currently, the API doesn't require authentication for testing purposes. In production, implement proper authentication mechanisms.

## Rate Limiting
The API implements rate limiting: 100 requests per 15 minutes per IP address.

## Error Responses
All endpoints return consistent error responses:
\`\`\`json
{
  "success": false,
  "error": "Error message description"
}
\`\`\`

## Status Codes
- 200: Success
- 201: Created
- 400: Bad Request (validation error)
- 404: Not Found
- 429: Too Many Requests (rate limited)
- 500: Internal Server Error
    `,
    contact: {
      name: 'SMS Gateway API Support',
      email: '<EMAIL>',
    },
    license: {
      name: 'ISC',
    },
  },
  servers: [
    {
      url: config.server.baseUrl,
      description: 'Development server',
    },
    {
      url: 'https://your-production-domain.com',
      description: 'Production server',
    },
  ],
  components: {
    schemas: {
      ApiResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            description: 'Indicates if the request was successful',
          },
          data: {
            type: 'object',
            description: 'Response data (varies by endpoint)',
          },
          error: {
            type: 'string',
            description: 'Error message (only present when success is false)',
          },
          message: {
            type: 'string',
            description: 'Additional message or description',
          },
        },
        required: ['success'],
      },
      PaginatedResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              pagination: {
                type: 'object',
                properties: {
                  page: { type: 'integer', description: 'Current page number' },
                  limit: { type: 'integer', description: 'Items per page' },
                  total: { type: 'integer', description: 'Total number of items' },
                  totalPages: { type: 'integer', description: 'Total number of pages' },
                },
                required: ['page', 'limit', 'total', 'totalPages'],
              },
            },
          },
        ],
      },
      SendSMSRequest: {
        type: 'object',
        required: ['phoneNumbers', 'message'],
        properties: {
          phoneNumbers: {
            type: 'array',
            items: { type: 'string' },
            description: 'Array of phone numbers in E.164 format (e.g., +1234567890)',
            example: ['+1234567890', '+0987654321'],
            maxItems: 10,
          },
          message: {
            type: 'string',
            description: 'SMS message content',
            example: 'Hello from SMS Gateway API!',
            maxLength: 1600,
          },
          ttl: {
            type: 'integer',
            description: 'Time to live in seconds (optional)',
            example: 3600,
          },
          simNumber: {
            type: 'integer',
            description: 'SIM card number to use (optional)',
            example: 1,
          },
          withDeliveryReport: {
            type: 'boolean',
            description: 'Request delivery report (optional)',
            example: true,
          },
          skipPhoneValidation: {
            type: 'boolean',
            description: 'Skip phone number validation (optional)',
            example: false,
          },
        },
      },
      SendSMSResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  messageId: {
                    type: 'string',
                    description: 'Unique message identifier',
                    example: '550e8400-e29b-41d4-a716-446655440000',
                  },
                  status: {
                    type: 'string',
                    enum: ['Pending', 'Processed', 'Sent', 'Delivered', 'Failed'],
                    description: 'Current message status',
                  },
                  recipients: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        phoneNumber: { type: 'string' },
                        state: {
                          type: 'string',
                          enum: ['Pending', 'Processed', 'Sent', 'Delivered', 'Failed'],
                        },
                        error: { type: 'string' },
                      },
                    },
                  },
                },
              },
            },
          },
        ],
      },
      MessageState: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Message ID',
            example: '550e8400-e29b-41d4-a716-446655440000',
          },
          state: {
            type: 'string',
            enum: ['Pending', 'Processed', 'Sent', 'Delivered', 'Failed'],
            description: 'Current message state',
          },
          recipients: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                phoneNumber: { type: 'string' },
                state: {
                  type: 'string',
                  enum: ['Pending', 'Processed', 'Sent', 'Delivered', 'Failed'],
                },
                error: { type: 'string' },
              },
            },
          },
        },
      },
      StoredMessage: {
        type: 'object',
        properties: {
          id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440000' },
          phoneNumbers: { type: 'string', example: '[\"+1234567890\"]' },
          message: { type: 'string', example: 'Hello from SMS Gateway API!' },
          status: {
            type: 'string',
            enum: ['Pending', 'Processed', 'Sent', 'Delivered', 'Failed'],
          },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
          deliveredAt: { type: 'string', format: 'date-time' },
          failedReason: { type: 'string' },
          lineUserId: { type: 'string' },
        },
      },
      Device: {
        type: 'object',
        properties: {
          id: { type: 'string', example: 'device-123' },
          name: { type: 'string', example: 'My Android Phone' },
          createdAt: { type: 'string', format: 'date-time' },
          lastSeen: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
          deletedAt: { type: 'string', format: 'date-time' },
        },
      },
      DeviceSettings: {
        type: 'object',
        properties: {
          messages: {
            type: 'object',
            properties: {
              limitPeriod: {
                type: 'string',
                enum: ['PerMinute', 'PerHour', 'PerDay'],
                example: 'PerDay',
              },
              limitValue: { type: 'integer', example: 100 },
            },
          },
          webhooks: {
            type: 'object',
            properties: {
              internetRequired: { type: 'boolean', example: true },
              retryCount: { type: 'integer', example: 3 },
            },
          },
          gateway: {
            type: 'object',
            properties: {
              name: { type: 'string', example: 'My SMS Gateway' },
            },
          },
        },
      },
      LineMessage: {
        type: 'object',
        required: ['userId', 'message'],
        properties: {
          userId: {
            type: 'string',
            description: 'LINE user ID',
            example: 'U503f97b9790a1fab78392736528b53ab',
          },
          message: {
            type: 'object',
            description: 'LINE message object',
            properties: {
              type: { type: 'string', example: 'text' },
              text: { type: 'string', example: 'Hello from API!' },
            },
          },
        },
      },
      WebHook: {
        type: 'object',
        properties: {
          id: { type: 'string', example: 'webhook-123' },
          event: {
            type: 'string',
            enum: ['sms:received', 'sms:sent', 'sms:delivered', 'sms:failed'],
          },
          url: { type: 'string', example: 'https://your-domain.com/webhook/sms' },
          deviceId: { type: 'string', example: 'device-123' },
        },
      },
      HealthResponse: {
        type: 'object',
        properties: {
          status: { type: 'string', example: 'healthy' },
          version: { type: 'string', example: '1.0.0' },
          timestamp: { type: 'string', format: 'date-time' },
        },
      },
    },
    parameters: {
      MessageId: {
        name: 'messageId',
        in: 'path',
        required: true,
        description: 'Message ID',
        schema: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
      },
      DeviceId: {
        name: 'deviceId',
        in: 'path',
        required: true,
        description: 'Device ID',
        schema: {
          type: 'string',
          example: 'device-123',
        },
      },
      WebhookId: {
        name: 'webhookId',
        in: 'path',
        required: true,
        description: 'Webhook ID',
        schema: {
          type: 'string',
          example: 'webhook-123',
        },
      },
      Page: {
        name: 'page',
        in: 'query',
        description: 'Page number for pagination',
        schema: {
          type: 'integer',
          minimum: 1,
          default: 1,
          example: 1,
        },
      },
      Limit: {
        name: 'limit',
        in: 'query',
        description: 'Number of items per page',
        schema: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
          default: 20,
          example: 20,
        },
      },
    },
  },
  tags: [
    {
      name: 'Health',
      description: 'Health check endpoints',
    },
    {
      name: 'SMS',
      description: 'SMS message operations',
    },
    {
      name: 'Devices',
      description: 'Device management operations',
    },
    {
      name: 'LINE Bot',
      description: 'LINE Bot integration endpoints',
    },
    {
      name: 'Webhooks',
      description: 'Webhook management operations',
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
  ],
};

const specs = swaggerJsdoc(options);

export default specs;
