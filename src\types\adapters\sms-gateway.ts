/**
 * Type adapters for android-sms-gateway library compatibility
 */

import * as LibraryTypes from 'android-sms-gateway';
import {
  SMSMessage,
  MessageState,
  RecipientState
} from '../sms/message';
import {
  WebHook,
  RegisterWebHookRequest
} from '../sms/webhook';
import {
  MessagesExportRequest
} from '../sms/gateway';
import { DeviceSettings, LimitPeriod } from '../device/settings';
import { Device } from '../device/device';

// Type adapters to bridge library types with our local types

// Message adapters
export type LibraryMessage = LibraryTypes.Message;
export type LibraryMessageState = LibraryTypes.MessageState;
export type LibraryRecipientState = LibraryTypes.RecipientState;

// Webhook adapters
export type LibraryWebHook = LibraryTypes.WebHook;
export type LibraryRegisterWebHookRequest = LibraryTypes.RegisterWebHookRequest;

// Device adapters
export type LibraryDevice = LibraryTypes.Device;
export type LibraryDeviceSettings = LibraryTypes.DeviceSettings;
export type LibraryLimitPeriod = LibraryTypes.LimitPeriod;

// Export request adapter
export type LibraryMessagesExportRequest = LibraryTypes.MessagesExportRequest;

// Conversion functions
export namespace Adapters {
  // Convert our SMSMessage to library Message
  export function toLibraryMessage(message: SMSMessage): LibraryMessage {
    return {
      id: message.id,
      message: message.message,
      ttl: message.ttl,
      phoneNumbers: message.phoneNumbers,
      simNumber: message.simNumber,
      withDeliveryReport: message.withDeliveryReport,
    };
  }

  // Convert library MessageState to our MessageState
  export function fromLibraryMessageState(state: LibraryMessageState): MessageState {
    return {
      id: state.id,
      state: state.state as any, // ProcessState enum should match
      recipients: state.recipients.map(fromLibraryRecipientState),
    };
  }

  // Convert library RecipientState to our RecipientState
  export function fromLibraryRecipientState(recipient: LibraryRecipientState): RecipientState {
    return {
      phoneNumber: recipient.phoneNumber,
      state: recipient.state as any, // ProcessState enum should match
      error: recipient.error,
    };
  }

  // Convert our RegisterWebHookRequest to library RegisterWebHookRequest
  export function toLibraryRegisterWebHookRequest(request: RegisterWebHookRequest): LibraryRegisterWebHookRequest {
    return {
      id: request.id,
      event: request.event as any, // WebHookEventType should match
      url: request.url,
      deviceId: request.deviceId,
    };
  }

  // Convert library WebHook to our WebHook
  export function fromLibraryWebHook(webhook: LibraryWebHook): WebHook {
    return {
      id: webhook.id || '', // Handle null case
      event: webhook.event as any,
      url: webhook.url,
      deviceId: webhook.deviceId || '',
      createdAt: new Date(), // Library doesn't provide timestamps
      updatedAt: new Date(),
    };
  }

  // Convert our MessagesExportRequest to library MessagesExportRequest
  export function toLibraryMessagesExportRequest(request: MessagesExportRequest): LibraryMessagesExportRequest {
    return {
      deviceId: request.deviceId,
      since: request.since, // Library expects Date, we provide Date
      until: request.until,
    };
  }

  // Convert library Device to our Device
  export function fromLibraryDevice(device: LibraryDevice): Device {
    return {
      id: device.id,
      name: device.name,
      createdAt: new Date(device.createdAt),
      updatedAt: new Date(device.updatedAt),
      lastSeen: device.lastSeen,
      deletedAt: device.deletedAt,
    };
  }

  // Convert our DeviceSettings to library DeviceSettings
  export function toLibraryDeviceSettings(settings: DeviceSettings): LibraryDeviceSettings {
    return {
      messages: settings.messages ? {
        limitPeriod: settings.messages.limitPeriod as LibraryLimitPeriod,
        limitValue: settings.messages.limitValue,
        logLifetimeDays: settings.messages.logLifetimeDays,
        sendIntervalMin: settings.messages.sendIntervalMin,
        sendIntervalMax: settings.messages.sendIntervalMax,
        simSelectionMode: settings.messages.simSelectionMode as any,
      } : undefined,
      webhooks: settings.webhooks ? {
        internetRequired: settings.webhooks.internetRequired,
        retryCount: settings.webhooks.retryCount,
        signingKey: settings.webhooks.signingKey,
      } : undefined,
      gateway: settings.gateway ? {
        cloudUrl: settings.gateway.cloudUrl,
        privateToken: settings.gateway.privateToken,
      } : undefined,
      encryption: settings.encryption ? {
        passphrase: settings.encryption.passphrase,
      } : undefined,
      logs: settings.logs ? {
        lifetimeDays: settings.logs.lifetimeDays,
      } : undefined,
      ping: settings.ping ? {
        intervalSeconds: settings.ping.intervalSeconds,
      } : undefined,
    };
  }

  // Convert library DeviceSettings to our DeviceSettings
  export function fromLibraryDeviceSettings(settings: LibraryDeviceSettings): DeviceSettings {
    return {
      messages: settings.messages ? {
        limitPeriod: settings.messages.limitPeriod as LimitPeriod,
        limitValue: settings.messages.limitValue,
        logLifetimeDays: settings.messages.logLifetimeDays,
        sendIntervalMin: settings.messages.sendIntervalMin,
        sendIntervalMax: settings.messages.sendIntervalMax,
        simSelectionMode: settings.messages.simSelectionMode as any,
      } : undefined,
      webhooks: settings.webhooks ? {
        internetRequired: settings.webhooks.internetRequired,
        retryCount: settings.webhooks.retryCount,
        signingKey: settings.webhooks.signingKey,
      } : undefined,
      gateway: settings.gateway ? {
        name: undefined, // Library doesn't have name field
        cloudUrl: settings.gateway.cloudUrl,
        privateToken: settings.gateway.privateToken,
      } : undefined,
      encryption: settings.encryption ? {
        enabled: undefined, // Library doesn't have enabled field
        passphrase: settings.encryption.passphrase,
      } : undefined,
      logs: settings.logs ? {
        ttl: undefined, // Library doesn't have ttl field
        lifetimeDays: settings.logs.lifetimeDays,
      } : undefined,
      ping: settings.ping ? {
        enabled: undefined, // Library doesn't have enabled field
        interval: undefined, // Library doesn't have interval field
        intervalSeconds: settings.ping.intervalSeconds,
      } : undefined,
    };
  }
}
