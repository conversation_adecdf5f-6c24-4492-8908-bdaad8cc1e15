/**
 * LINE user types and interfaces
 */

import { BaseEntity } from '../base/common';

// LINE user entity
export interface LineUser extends BaseEntity {
  displayName?: string | undefined;
  pictureUrl?: string | undefined;
  statusMessage?: string | undefined;
  isAdmin: boolean;
}

// LINE user creation data
export interface CreateLineUserData {
  id: string;
  displayName?: string | undefined;
  pictureUrl?: string | undefined;
  statusMessage?: string | undefined;
  isAdmin?: boolean | undefined;
}

// LINE user update data
export interface UpdateLineUserData {
  displayName?: string | undefined;
  pictureUrl?: string | undefined;
  statusMessage?: string | undefined;
  isAdmin?: boolean | undefined;
}

// LINE user filter
export interface LineUserFilter {
  displayName?: string;
  isAdmin?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
}
