/**
 * LINE user types and interfaces
 */

import { BaseEntity } from '../base/common';

// LINE user entity
export interface LineUser extends BaseEntity {
  displayName?: string;
  pictureUrl?: string;
  statusMessage?: string;
  isAdmin: boolean;
}

// LINE user creation data
export interface CreateLineUserData {
  id: string;
  displayName?: string;
  pictureUrl?: string;
  statusMessage?: string;
  isAdmin?: boolean;
}

// LINE user update data
export interface UpdateLineUserData {
  displayName?: string;
  pictureUrl?: string;
  statusMessage?: string;
  isAdmin?: boolean;
}

// LINE user filter
export interface LineUserFilter {
  displayName?: string;
  isAdmin?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
}
