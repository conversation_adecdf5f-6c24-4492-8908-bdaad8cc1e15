import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';

export const validatePhoneNumber = (phoneNumber: string): boolean => {
  // Basic phone number validation (E.164 format)
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phoneNumber);
};

export const validateSMSMessage = (req: Request, res: Response, next: NextFunction): void => {
  const { phoneNumbers, message } = req.body;

  if (!phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
    throw new AppError('Phone numbers are required and must be a non-empty array', 400);
  }

  if (!message || typeof message !== 'string' || message.trim().length === 0) {
    throw new AppError('Message is required and must be a non-empty string', 400);
  }

  if (message.length > 1600) {
    throw new AppError('Message is too long (maximum 1600 characters)', 400);
  }

  if (phoneNumbers.length > 10) {
    throw new AppError('Too many phone numbers (maximum 10 per request)', 400);
  }

  // Validate each phone number
  const invalidNumbers = phoneNumbers.filter((num: string) => !validatePhoneNumber(num));
  if (invalidNumbers.length > 0) {
    throw new AppError(`Invalid phone numbers: ${invalidNumbers.join(', ')}`, 400);
  }

  next();
};

export const validatePagination = (req: Request, res: Response, next: NextFunction): void => {
  const { page, limit } = req.query;

  if (page && (isNaN(Number(page)) || Number(page) < 1)) {
    throw new AppError('Page must be a positive integer', 400);
  }

  if (limit && (isNaN(Number(limit)) || Number(limit) < 1 || Number(limit) > 100)) {
    throw new AppError('Limit must be a positive integer between 1 and 100', 400);
  }

  next();
};

export const validateMessageId = (req: Request, res: Response, next: NextFunction): void => {
  const { messageId } = req.params;

  if (!messageId || typeof messageId !== 'string' || messageId.trim().length === 0) {
    throw new AppError('Message ID is required', 400);
  }

  next();
};

export const validateWebhookSignature = (secret: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const signature = req.headers['x-webhook-signature'] as string;
    
    if (!signature) {
      throw new AppError('Webhook signature is required', 401);
    }

    // In a real implementation, you would verify the signature
    // using HMAC-SHA256 with the webhook secret
    // For now, we'll just check if the signature matches the secret
    if (signature !== secret) {
      throw new AppError('Invalid webhook signature', 401);
    }

    next();
  };
};

export const validateLineSignature = (channelSecret: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const signature = req.headers['x-line-signature'] as string;
    
    if (!signature) {
      throw new AppError('LINE signature is required', 401);
    }

    // LINE signature validation would be implemented here
    // using the @line/bot-sdk's validateSignature function
    // For now, we'll skip the actual validation
    
    next();
  };
};
