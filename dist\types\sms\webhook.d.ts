import { BaseEntity } from '../base/common';
export declare enum WebHookEventType {
    SmsReceived = "sms:received",
    SmsSent = "sms:sent",
    SmsDelivered = "sms:delivered",
    SmsFailed = "sms:failed",
    SystemPing = "system:ping"
}
export interface WebHook extends BaseEntity {
    event: WebHookEventType;
    url: string;
    deviceId: string;
    active?: boolean;
}
export interface RegisterWebHookRequest {
    id?: string | null;
    event: WebHookEventType;
    url: string;
    deviceId?: string | null;
}
export interface SmsReceivedPayload {
    message: string;
    phoneNumber: string;
    receivedAt: string;
}
export interface SmsSentPayload {
    messageId: string;
    sentAt: string;
}
export interface SmsDeliveredPayload {
    messageId: string;
    deliveredAt: string;
}
export interface SmsFailedPayload {
    messageId: string;
    failedAt: string;
    error: string;
}
export interface SystemPingPayload {
}
export type WebHookPayload = {
    event: WebHookEventType.SmsReceived;
    payload: SmsReceivedPayload;
} | {
    event: WebHookEventType.SmsSent;
    payload: SmsSentPayload;
} | {
    event: WebHookEventType.SmsDelivered;
    payload: SmsDeliveredPayload;
} | {
    event: WebHookEventType.SmsFailed;
    payload: SmsFailedPayload;
} | {
    event: WebHookEventType.SystemPing;
    payload: SystemPingPayload;
};
export interface WebhookLog extends BaseEntity {
    type: 'sms' | 'line';
    payload: string;
    processed: boolean;
    processedAt?: Date | undefined;
    error?: string | undefined;
}
//# sourceMappingURL=webhook.d.ts.map