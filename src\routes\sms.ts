import { Router } from 'express';
import smsController from '../controllers/smsController';
import {
  validateSMSMessage,
  validateMessageId,
  validatePagination,
} from '../middleware/validation';

const router = Router();

/**
 * @swagger
 * /api/sms/send:
 *   post:
 *     tags: [SMS]
 *     summary: Send SMS message
 *     description: Send SMS message to one or more phone numbers
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SendSMSRequest'
 *           examples:
 *             single_recipient:
 *               summary: Single recipient
 *               value:
 *                 phoneNumbers: ["+1234567890"]
 *                 message: "Hello from SMS Gateway API!"
 *                 withDeliveryReport: true
 *             multiple_recipients:
 *               summary: Multiple recipients
 *               value:
 *                 phoneNumbers: ["+1234567890", "+0987654321"]
 *                 message: "Broadcast message to multiple recipients"
 *                 withDeliveryReport: true
 *                 ttl: 3600
 *     responses:
 *       201:
 *         description: SMS message sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SendSMSResponse'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: false
 *               error: "Phone numbers are required and must be a non-empty array"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
router.post('/send', validateSMSMessage, smsController.sendMessage);

/**
 * @swagger
 * /api/sms/status/{messageId}:
 *   get:
 *     tags: [SMS]
 *     summary: Get message status
 *     description: Get the current status of an SMS message
 *     parameters:
 *       - $ref: '#/components/parameters/MessageId'
 *     responses:
 *       200:
 *         description: Message status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/MessageState'
 *             example:
 *               success: true
 *               data:
 *                 id: "550e8400-e29b-41d4-a716-446655440000"
 *                 state: "Delivered"
 *                 recipients:
 *                   - phoneNumber: "+1234567890"
 *                     state: "Delivered"
 *       404:
 *         description: Message not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: false
 *               error: "Message not found"
 */
router.get('/status/:messageId', validateMessageId, smsController.getMessageStatus);

/**
 * @route   POST /api/sms/status/:messageId/refresh
 * @desc    Refresh message status from SMS Gateway
 * @access  Public
 */
router.post('/status/:messageId/refresh', validateMessageId, smsController.refreshMessageStatus);

/**
 * @swagger
 * /api/sms/messages:
 *   get:
 *     tags: [SMS]
 *     summary: Get messages with filtering and pagination
 *     description: Retrieve SMS messages with optional filtering and pagination
 *     parameters:
 *       - $ref: '#/components/parameters/Page'
 *       - $ref: '#/components/parameters/Limit'
 *       - name: status
 *         in: query
 *         description: Filter by message status
 *         schema:
 *           type: string
 *           enum: [Pending, Processed, Sent, Delivered, Failed]
 *           example: Delivered
 *       - name: phoneNumber
 *         in: query
 *         description: Filter by phone number
 *         schema:
 *           type: string
 *           example: "+1234567890"
 *       - name: lineUserId
 *         in: query
 *         description: Filter by LINE user ID
 *         schema:
 *           type: string
 *           example: "U503f97b9790a1fab78392736528b53ab"
 *       - name: dateFrom
 *         in: query
 *         description: Filter messages from this date (ISO 8601)
 *         schema:
 *           type: string
 *           format: date-time
 *           example: "2024-01-01T00:00:00Z"
 *       - name: dateTo
 *         in: query
 *         description: Filter messages until this date (ISO 8601)
 *         schema:
 *           type: string
 *           format: date-time
 *           example: "2024-01-31T23:59:59Z"
 *     responses:
 *       200:
 *         description: Messages retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/StoredMessage'
 */
router.get('/messages', validatePagination, smsController.getMessages);

/**
 * @route   GET /api/sms/messages/:messageId
 * @desc    Get message details
 * @access  Public
 */
router.get('/messages/:messageId', validateMessageId, smsController.getMessageDetails);

/**
 * @swagger
 * /api/sms/stats:
 *   get:
 *     tags: [SMS]
 *     summary: Get message statistics
 *     description: Get SMS message statistics and analytics
 *     parameters:
 *       - name: dateFrom
 *         in: query
 *         description: Start date for statistics (ISO 8601)
 *         schema:
 *           type: string
 *           format: date-time
 *           example: "2024-01-01T00:00:00Z"
 *       - name: dateTo
 *         in: query
 *         description: End date for statistics (ISO 8601)
 *         schema:
 *           type: string
 *           format: date-time
 *           example: "2024-01-31T23:59:59Z"
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: Total number of messages
 *                         pending:
 *                           type: integer
 *                           description: Number of pending messages
 *                         sent:
 *                           type: integer
 *                           description: Number of sent messages
 *                         delivered:
 *                           type: integer
 *                           description: Number of delivered messages
 *                         failed:
 *                           type: integer
 *                           description: Number of failed messages
 *                         byDate:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                                 format: date
 *                               count:
 *                                 type: integer
 */
router.get('/stats', smsController.getMessageStats);

/**
 * @swagger
 * /api/sms/health:
 *   get:
 *     tags: [Health]
 *     summary: SMS service health check
 *     description: Check the health status of the SMS service
 *     responses:
 *       200:
 *         description: SMS service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/HealthResponse'
 *             example:
 *               success: true
 *               data:
 *                 smsGateway: "connected"
 *                 timestamp: "2024-01-01T12:00:00Z"
 *               message: "SMS service is healthy"
 */
router.get('/health', smsController.healthCheck);

export default router;
