/**
 * Device settings types and interfaces
 */

// Limit period enum
export enum LimitPeriod {
  Disabled = 'Disabled',
  PerMinute = 'PerMinute',
  PerHour = 'PerHour',
  PerDay = 'PerDay'
}

// SIM selection mode
export enum SimSelectionMode {
  OSDefault = 'OSDefault',
  RoundRobin = 'RoundRobin',
  Random = 'Random'
}

// Settings interfaces
export interface SettingsMessages {
  limitPeriod?: LimitPeriod;
  limitValue?: number;
  logLifetimeDays?: number;
  sendIntervalMin?: number;
  sendIntervalMax?: number;
  simSelectionMode?: SimSelectionMode;
}

export interface SettingsWebhooks {
  internetRequired?: boolean;
  retryCount?: number;
  signingKey?: string;
}

export interface SettingsGateway {
  name?: string;
  cloudUrl?: string;
  privateToken?: string;
}

export interface SettingsEncryption {
  enabled?: boolean;
  passphrase?: string;
}

export interface SettingsLogs {
  ttl?: number;
  lifetimeDays?: number;
}

export interface SettingsPing {
  enabled?: boolean;
  interval?: number;
  intervalSeconds?: number;
}

// Main device settings interface
export interface DeviceSettings {
  messages?: SettingsMessages;
  webhooks?: SettingsWebhooks;
  gateway?: SettingsGateway;
  encryption?: SettingsEncryption;
  logs?: SettingsLogs;
  ping?: SettingsPing;
}

// Settings update requests
export interface UpdateSettingsRequest extends DeviceSettings {}
export interface PatchSettingsRequest extends Partial<DeviceSettings> {}
