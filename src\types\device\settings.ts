/**
 * Device settings types and interfaces
 */

// Limit period enum
export enum LimitPeriod {
  Disabled = 'Disabled',
  PerMinute = 'PerMinute',
  PerHour = 'PerHour',
  PerDay = 'PerDay'
}

// SIM selection mode
export enum SimSelectionMode {
  OSDefault = 'OSDefault',
  RoundRobin = 'RoundRobin',
  Random = 'Random'
}

// Settings interfaces
export interface SettingsMessages {
  limitPeriod?: LimitPeriod | undefined;
  limitValue?: number | undefined;
  logLifetimeDays?: number | undefined;
  sendIntervalMin?: number | undefined;
  sendIntervalMax?: number | undefined;
  simSelectionMode?: SimSelectionMode | undefined;
}

export interface SettingsWebhooks {
  internetRequired?: boolean | undefined;
  retryCount?: number | undefined;
  signingKey?: string | undefined;
}

export interface SettingsGateway {
  name?: string | undefined;
  cloudUrl?: string | undefined;
  privateToken?: string | undefined;
}

export interface SettingsEncryption {
  enabled?: boolean | undefined;
  passphrase?: string | undefined;
}

export interface SettingsLogs {
  ttl?: number | undefined;
  lifetimeDays?: number | undefined;
}

export interface SettingsPing {
  enabled?: boolean | undefined;
  interval?: number | undefined;
  intervalSeconds?: number | undefined;
}

// Main device settings interface
export interface DeviceSettings {
  messages?: SettingsMessages | undefined;
  webhooks?: SettingsWebhooks | undefined;
  gateway?: SettingsGateway | undefined;
  encryption?: SettingsEncryption | undefined;
  logs?: SettingsLogs | undefined;
  ping?: SettingsPing | undefined;
}

// Settings update requests
export interface UpdateSettingsRequest extends DeviceSettings {}
export interface PatchSettingsRequest extends Partial<DeviceSettings> {}
