#!/usr/bin/env node

/**
 * Test script to verify SMS Gateway API setup
 * Run with: node scripts/test-setup.js
 */

// Load environment variables
require('dotenv').config();

const http = require('http');
const https = require('https');

const BASE_URL = process.env.APP_BASE_URL || 'http://localhost:3000';

// Test configuration
const tests = [
  {
    name: 'Health Check',
    path: '/health',
    method: 'GET',
    expected: { success: true }
  },
  {
    name: 'SMS Health Check',
    path: '/api/sms/health',
    method: 'GET',
    expected: { success: true }
  },
  {
    name: 'LINE Bot Health Check',
    path: '/api/line/health',
    method: 'GET',
    expected: { success: true }
  },
  {
    name: 'Device Health Check',
    path: '/api/devices/health',
    method: 'GET',
    expected: { success: true }
  },
  {
    name: 'Webhook Health Check',
    path: '/webhook/health',
    method: 'GET',
    expected: { success: true }
  },
  {
    name: 'API Documentation',
    path: '/api',
    method: 'GET',
    expected: { success: true }
  }
];

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    
    const req = client.request(url, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Test runner
async function runTests() {
  console.log('🚀 Starting SMS Gateway API Tests');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log('=' .repeat(50));
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      console.log(`\n🧪 Testing: ${test.name}`);
      
      const url = `${BASE_URL}${test.path}`;
      const options = {
        method: test.method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'SMS-Gateway-Test-Script/1.0'
        }
      };
      
      if (test.body) {
        options.body = JSON.stringify(test.body);
      }
      
      const response = await makeRequest(url, options);
      
      // Check status code
      if (response.statusCode !== 200) {
        console.log(`❌ Failed: HTTP ${response.statusCode}`);
        console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
        failed++;
        continue;
      }
      
      // Check expected response
      if (test.expected) {
        const hasExpectedFields = Object.keys(test.expected).every(key => {
          return response.data && response.data[key] === test.expected[key];
        });
        
        if (!hasExpectedFields) {
          console.log(`❌ Failed: Response doesn't match expected format`);
          console.log(`   Expected: ${JSON.stringify(test.expected)}`);
          console.log(`   Received: ${JSON.stringify(response.data)}`);
          failed++;
          continue;
        }
      }
      
      console.log(`✅ Passed: ${test.name}`);
      if (response.data && response.data.message) {
        console.log(`   Message: ${response.data.message}`);
      }
      passed++;
      
    } catch (error) {
      console.log(`❌ Failed: ${test.name}`);
      console.log(`   Error: ${error.message}`);
      failed++;
    }
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log(`📊 Test Results:`);
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Your SMS Gateway API is ready to use.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check your configuration and try again.');
    process.exit(1);
  }
}

// Configuration check
function checkConfiguration() {
  console.log('\n🔧 Configuration Check:');
  
  const requiredEnvVars = [
    'SMS_GATEWAY_LOGIN',
    'SMS_GATEWAY_PASSWORD',
    'LINE_CHANNEL_ACCESS_TOKEN',
    'LINE_CHANNEL_SECRET'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log(`❌ Missing required environment variables:`);
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log('\n💡 Please check your .env file and ensure all required variables are set.');
    return false;
  }
  
  console.log('✅ All required environment variables are set');
  
  // Check optional configurations
  const optionalVars = [
    'LINE_ADMIN_USER_ID',
    'SMS_WEBHOOK_SECRET',
    'DATABASE_PATH'
  ];
  
  console.log('\n📋 Optional configurations:');
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`   ✅ ${varName}: ${value.substring(0, 20)}${value.length > 20 ? '...' : ''}`);
    } else {
      console.log(`   ⚪ ${varName}: Not set`);
    }
  });
  
  return true;
}

// Main execution
async function main() {
  console.log('SMS Gateway API Test Suite');
  console.log('Version 1.0.0\n');
  
  // Check if server is likely running
  if (BASE_URL.includes('localhost') || BASE_URL.includes('127.0.0.1')) {
    console.log('💡 Make sure your SMS Gateway API server is running before running tests.');
    console.log('   Start with: npm run dev or npm start\n');
  }
  
  // Check configuration
  const configOk = checkConfiguration();
  if (!configOk) {
    process.exit(1);
  }
  
  // Run tests
  await runTests();
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('\n💥 Unhandled error:', error.message);
  process.exit(1);
});

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('\n💥 Test suite failed:', error.message);
    process.exit(1);
  });
}

module.exports = { runTests, checkConfiguration };
