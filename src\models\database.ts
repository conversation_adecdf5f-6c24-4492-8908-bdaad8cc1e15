import sqlite3 from 'sqlite3';
import path from 'path';
import fs from 'fs';
import config from '../config';
import logger from '../utils/logger';

class Database {
  private db: sqlite3.Database | null = null;
  private dbPath: string;

  constructor() {
    this.dbPath = config.database.path;
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Ensure database directory exists
      const dbDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          logger.error('Failed to connect to database', { error: err.message });
          reject(err);
        } else {
          logger.info('Connected to SQLite database', { path: this.dbPath });
          resolve();
        }
      });
    });
  }

  async disconnect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            logger.error('Failed to close database', { error: err.message });
            reject(err);
          } else {
            logger.info('Database connection closed');
            this.db = null;
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  async run(sql: string, params: any[] = []): Promise<sqlite3.RunResult> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not connected'));
        return;
      }

      this.db.run(sql, params, function(err) {
        if (err) {
          logger.error('Database run error', { error: err.message, sql, params });
          reject(err);
        } else {
          resolve(this);
        }
      });
    });
  }

  async get<T = any>(sql: string, params: any[] = []): Promise<T | undefined> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not connected'));
        return;
      }

      this.db.get(sql, params, (err, row) => {
        if (err) {
          logger.error('Database get error', { error: err.message, sql, params });
          reject(err);
        } else {
          resolve(row as T);
        }
      });
    });
  }

  async all<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not connected'));
        return;
      }

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          logger.error('Database all error', { error: err.message, sql, params });
          reject(err);
        } else {
          resolve(rows as T[]);
        }
      });
    });
  }

  async initializeTables(): Promise<void> {
    const tables = [
      // Messages table
      `CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        phone_numbers TEXT NOT NULL,
        message TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'Pending',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        delivered_at DATETIME,
        failed_reason TEXT,
        line_user_id TEXT,
        ttl INTEGER,
        sim_number INTEGER,
        with_delivery_report BOOLEAN DEFAULT 0
      )`,

      // LINE users table
      `CREATE TABLE IF NOT EXISTS line_users (
        id TEXT PRIMARY KEY,
        display_name TEXT,
        picture_url TEXT,
        status_message TEXT,
        is_admin BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // LINE messages table
      `CREATE TABLE IF NOT EXISTS line_messages (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        message_type TEXT NOT NULL,
        content TEXT NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        reply_token TEXT,
        FOREIGN KEY (user_id) REFERENCES line_users (id)
      )`,

      // Webhook logs table
      `CREATE TABLE IF NOT EXISTS webhook_logs (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        payload TEXT NOT NULL,
        processed BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        processed_at DATETIME,
        error TEXT
      )`,

      // Message recipients table (for tracking individual recipient status)
      `CREATE TABLE IF NOT EXISTS message_recipients (
        id TEXT PRIMARY KEY,
        message_id TEXT NOT NULL,
        phone_number TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'Pending',
        error TEXT,
        delivered_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (message_id) REFERENCES messages (id)
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Webhooks table
      `CREATE TABLE IF NOT EXISTS webhooks (
        id TEXT PRIMARY KEY,
        url TEXT NOT NULL,
        event TEXT NOT NULL,
        device_id TEXT,
        active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
    ];

    for (const table of tables) {
      await this.run(table);
    }

    // Create indexes for better performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_messages_status ON messages (status)',
      'CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages (created_at)',
      'CREATE INDEX IF NOT EXISTS idx_messages_line_user_id ON messages (line_user_id)',
      'CREATE INDEX IF NOT EXISTS idx_message_recipients_message_id ON message_recipients (message_id)',
      'CREATE INDEX IF NOT EXISTS idx_message_recipients_phone_number ON message_recipients (phone_number)',
      'CREATE INDEX IF NOT EXISTS idx_line_messages_user_id ON line_messages (user_id)',
      'CREATE INDEX IF NOT EXISTS idx_line_messages_timestamp ON line_messages (timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_webhook_logs_type ON webhook_logs (type)',
      'CREATE INDEX IF NOT EXISTS idx_webhook_logs_processed ON webhook_logs (processed)',
      'CREATE INDEX IF NOT EXISTS idx_webhooks_active ON webhooks (active)',
    ];

    for (const index of indexes) {
      await this.run(index);
    }

    logger.info('Database tables and indexes initialized successfully');
  }

  async migrate(): Promise<void> {
    // Add any database migrations here
    logger.info('Database migrations completed');
  }
}

// Create singleton instance
const database = new Database();

export default database;
