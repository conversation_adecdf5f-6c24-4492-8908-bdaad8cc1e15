import Client, { LogEntry } from 'android-sms-gateway';
import config from '../config';
import FetchHttpClient from './httpClient';
import logger from '../utils/logger';
import {
  SMSMessage,
  MessageState,
  WebHook,
  Device,
  DeviceSettings,
  RegisterWebHookRequest,
  MessagesExportRequest,
  Adapters,
} from '../types';

interface HealthResponse {
  status: string;
  version: string;
  releaseId: number;
  checks: { [checkName: string]: any };
}

class SMSGatewayClient {
  private client: Client;
  private httpClient: FetchHttpClient;

  constructor() {
    this.httpClient = new FetchHttpClient();
    this.client = new Client(
      config.smsGateway.login,
      config.smsGateway.password,
      this.httpClient,
      config.smsGateway.baseUrl
    );

    logger.info('SMS Gateway client initialized', {
      baseUrl: config.smsGateway.baseUrl,
      login: config.smsGateway.login,
    });
  }

  /**
   * Test connection to SMS Gateway
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.getHealth();
      logger.info('SMS Gateway connection test successful');
      return true;
    } catch (error) {
      logger.error('SMS Gateway connection test failed', {
        error: (error as Error).message,
      });
      return false;
    }
  }

  /**
   * Send SMS message
   */
  async sendMessage(
    message: SMSMessage,
    options?: { skipPhoneValidation?: boolean }
  ): Promise<MessageState> {
    try {
      logger.info('Sending SMS message', {
        phoneNumbers: message.phoneNumbers,
        messageLength: message.message.length,
        options,
      });

      const libraryMessage = Adapters.toLibraryMessage(message);
      const libraryResult = await this.client.send(libraryMessage, options);
      const result = Adapters.fromLibraryMessageState(libraryResult);

      logger.info('SMS message sent successfully', {
        messageId: result.id,
        state: result.state,
        recipientCount: result.recipients.length,
      });

      return result;
    } catch (error) {
      logger.error('Failed to send SMS message', {
        error: (error as Error).message,
        phoneNumbers: message.phoneNumbers,
      });
      throw error;
    }
  }

  /**
   * Get message status
   */
  async getMessageState(messageId: string): Promise<MessageState> {
    try {
      logger.debug('Getting message state', { messageId });
      const libraryResult = await this.client.getState(messageId);
      const result = Adapters.fromLibraryMessageState(libraryResult);

      logger.debug('Message state retrieved', {
        messageId,
        state: result.state,
        recipientCount: result.recipients.length,
      });

      return result;
    } catch (error) {
      logger.error('Failed to get message state', {
        error: (error as Error).message,
        messageId,
      });
      throw error;
    }
  }

  /**
   * Get registered webhooks
   */
  async getWebhooks(): Promise<WebHook[]> {
    try {
      logger.debug('Getting webhooks');
      const libraryResult = await this.client.getWebhooks();
      const result = libraryResult.map(webhook => Adapters.fromLibraryWebHook(webhook));

      logger.debug('Webhooks retrieved', { count: result.length });
      return result;
    } catch (error) {
      logger.error('Failed to get webhooks', {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Register webhook
   */
  async registerWebhook(request: RegisterWebHookRequest): Promise<WebHook> {
    try {
      logger.info('Registering webhook', request);
      const libraryRequest = Adapters.toLibraryRegisterWebHookRequest(request);
      const libraryResult = await this.client.registerWebhook(libraryRequest);
      const result = Adapters.fromLibraryWebHook(libraryResult);

      logger.info('Webhook registered successfully', {
        webhookId: result.id,
        url: result.url,
        event: result.event,
      });

      return result;
    } catch (error) {
      logger.error('Failed to register webhook', {
        error: (error as Error).message,
        request,
      });
      throw error;
    }
  }

  /**
   * Delete webhook
   */
  async deleteWebhook(webhookId: string): Promise<void> {
    try {
      logger.info('Deleting webhook', { webhookId });
      await this.client.deleteWebhook(webhookId);
      
      logger.info('Webhook deleted successfully', { webhookId });
    } catch (error) {
      logger.error('Failed to delete webhook', {
        error: (error as Error).message,
        webhookId,
      });
      throw error;
    }
  }

  /**
   * Get devices
   */
  async getDevices(): Promise<Device[]> {
    try {
      logger.debug('Getting devices');
      const libraryResult = await this.client.getDevices();
      const result = libraryResult.map(device => Adapters.fromLibraryDevice(device));

      logger.debug('Devices retrieved', { count: result.length });
      return result;
    } catch (error) {
      logger.error('Failed to get devices', {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Delete device
   */
  async deleteDevice(deviceId: string): Promise<void> {
    try {
      logger.info('Deleting device', { deviceId });
      await this.client.deleteDevice(deviceId);
      
      logger.info('Device deleted successfully', { deviceId });
    } catch (error) {
      logger.error('Failed to delete device', {
        error: (error as Error).message,
        deviceId,
      });
      throw error;
    }
  }

  /**
   * Get system health
   */
  async getHealth(): Promise<HealthResponse> {
    try {
      const result = await this.client.getHealth();
      logger.debug('Health check completed', { status: result.status });
      return result;
    } catch (error) {
      logger.error('Health check failed', {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Export inbox messages
   */
  async exportInbox(request: MessagesExportRequest): Promise<void> {
    try {
      logger.info('Exporting inbox messages', request);
      await this.client.exportInbox(request);
      
      logger.info('Inbox export requested successfully', request);
    } catch (error) {
      logger.error('Failed to export inbox', {
        error: (error as Error).message,
        request,
      });
      throw error;
    }
  }

  /**
   * Get logs
   */
  async getLogs(from?: Date, to?: Date): Promise<GatewayLogEntry[]> {
    try {
      logger.debug('Getting logs', { from, to });
      const result = await this.client.getLogs(from, to);
      
      logger.debug('Logs retrieved', { count: result.length });
      return result;
    } catch (error) {
      logger.error('Failed to get logs', {
        error: (error as Error).message,
        from,
        to,
      });
      throw error;
    }
  }

  /**
   * Get device settings
   */
  async getSettings(): Promise<DeviceSettings> {
    try {
      logger.debug('Getting device settings');
      const result = await this.client.getSettings();
      
      logger.debug('Device settings retrieved');
      return result;
    } catch (error) {
      logger.error('Failed to get device settings', {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Update device settings
   */
  async updateSettings(settings: DeviceSettings): Promise<void> {
    try {
      logger.info('Updating device settings', settings);
      await this.client.updateSettings(settings);
      
      logger.info('Device settings updated successfully');
    } catch (error) {
      logger.error('Failed to update device settings', {
        error: (error as Error).message,
        settings,
      });
      throw error;
    }
  }

  /**
   * Partially update device settings
   */
  async patchSettings(settings: Partial<DeviceSettings>): Promise<void> {
    try {
      logger.info('Partially updating device settings', settings);
      await this.client.patchSettings(settings);
      
      logger.info('Device settings partially updated successfully');
    } catch (error) {
      logger.error('Failed to partially update device settings', {
        error: (error as Error).message,
        settings,
      });
      throw error;
    }
  }
}

// Create singleton instance
const smsGatewayClient = new SMSGatewayClient();

export default smsGatewayClient;
