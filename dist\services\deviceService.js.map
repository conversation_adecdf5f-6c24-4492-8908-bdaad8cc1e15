{"version": 3, "file": "deviceService.js", "sourceRoot": "", "sources": ["../../src/services/deviceService.ts"], "names": [], "mappings": ";;;;;AAAA,0EAAkD;AAElD,6DAAqC;AACrC,6DAAsD;AAEtD,MAAM,aAAa;IAIjB,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,gBAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAEhC,MAAM,OAAO,GAAG,MAAM,0BAAgB,CAAC,UAAU,EAAE,CAAC;YAEpD,gBAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAE7D,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACpC,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,IAAI,uBAAQ,CAChB,0BAA2B,KAAe,CAAC,OAAO,EAAE,EACpD,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,QAAgB;QAC9B,IAAI,CAAC;YACH,gBAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;YAEpD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,uBAAQ,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;YAC9C,CAAC;YAED,gBAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAExE,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACnC,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,QAAQ;aACT,CAAC,CAAC;YAEH,IAAI,KAAK,YAAY,uBAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,uBAAQ,CAChB,yBAA0B,KAAe,CAAC,OAAO,EAAE,EACnD,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC;YACH,gBAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE7C,MAAM,0BAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAE9C,gBAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBACtC,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,QAAQ;aACT,CAAC,CAAC;YAEH,MAAM,IAAI,uBAAQ,CAChB,4BAA6B,KAAe,CAAC,OAAO,EAAE,EACtD,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,gBAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAExC,MAAM,QAAQ,GAAG,MAAM,0BAAgB,CAAC,WAAW,EAAE,CAAC;YAEtD,gBAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAE1C,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,IAAI,uBAAQ,CAChB,kCAAmC,KAAe,CAAC,OAAO,EAAE,EAC5D,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,QAAwB;QAC3C,IAAI,CAAC;YACH,gBAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;YAElD,MAAM,0BAAgB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAEhD,gBAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBAC/C,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,QAAQ;aACT,CAAC,CAAC;YAEH,MAAM,IAAI,uBAAQ,CAChB,qCAAsC,KAAe,CAAC,OAAO,EAAE,EAC/D,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,QAAiC;QACnD,IAAI,CAAC;YACH,gBAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE,QAAQ,CAAC,CAAC;YAE5D,MAAM,0BAAgB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAE/C,gBAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE;gBACzD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,QAAQ;aACT,CAAC,CAAC;YAEH,MAAM,IAAI,uBAAQ,CAChB,+CAAgD,KAAe,CAAC,OAAO,EAAE,EACzE,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS;QACb,IAAI,CAAC;YACH,gBAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAEtC,MAAM,MAAM,GAAG,MAAM,0BAAgB,CAAC,SAAS,EAAE,CAAC;YAElD,gBAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAEnE,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC1C,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,IAAI,uBAAQ,CAChB,gCAAiC,KAAe,CAAC,OAAO,EAAE,EAC1D,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,IAAW,EAAE,EAAS;QAClC,IAAI,CAAC;YACH,gBAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YAElD,MAAM,IAAI,GAAG,MAAM,0BAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAEtD,gBAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YAE9D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBACxC,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,IAAI;gBACJ,EAAE;aACH,CAAC,CAAC;YAEH,MAAM,IAAI,uBAAQ,CAChB,8BAA+B,KAAe,CAAC,OAAO,EAAE,EACxD,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,KAAW,EAAE,KAAW;QAC1D,IAAI,CAAC;YACH,gBAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;YAEpE,MAAM,0BAAgB,CAAC,WAAW,CAAC;gBACjC,QAAQ;gBACR,KAAK;gBACL,KAAK;aACN,CAAC,CAAC;YAEH,gBAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QACjF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBACrC,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,QAAQ;gBACR,KAAK;gBACL,KAAK;aACN,CAAC,CAAC;YAEH,MAAM,IAAI,uBAAQ,CAChB,2BAA4B,KAAe,CAAC,OAAO,EAAE,EACrD,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc;QAMlB,IAAI,CAAC;YACH,gBAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAE1C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAC5D,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAEhE,MAAM,KAAK,GAAG;gBACZ,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM;gBACvD,mBAAmB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACtC,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,SAAS,CACjC,CAAC,MAAM;gBACR,eAAe,EAAE,EAA8C;aAChE,CAAC;YAGF,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBAClD,IAAI,MAAM,GAAG,SAAS,CAAC;gBAEvB,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;oBACrB,MAAM,GAAG,SAAS,CAAC;gBACrB,CAAC;qBAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,UAAU,EAAE,CAAC;oBAClD,MAAM,GAAG,QAAQ,CAAC;gBACpB,CAAC;qBAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,SAAS,EAAE,CAAC;oBACjD,MAAM,GAAG,eAAe,CAAC;gBAC3B,CAAC;gBAED,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;gBACrC,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAA4B,CAAC,CAAC;YAEjC,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;iBACjD,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YAEjD,gBAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YAEpD,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBAC9C,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,IAAI,uBAAQ,CAChB,oCAAqC,KAAe,CAAC,OAAO,EAAE,EAC9D,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,gBAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAEzC,MAAM,WAAW,GAAG,MAAM,0BAAgB,CAAC,cAAc,EAAE,CAAC;YAE5D,gBAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;YAEjE,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAED,kBAAe,IAAI,aAAa,EAAE,CAAC"}