/**
 * LINE message types and interfaces
 */

import { BaseEntity } from '../base/common';

// LINE message entity
export interface LineMessage extends BaseEntity {
  userId: string;
  messageType: string;
  content: string;
  timestamp: Date;
  replyToken?: string;
}

// LINE message creation data
export interface CreateLineMessageData {
  userId: string;
  messageType: string;
  content: string;
  timestamp?: Date;
  replyToken?: string;
}

// LINE quick action types
export interface LineQuickAction {
  type: 'send_sms' | 'check_status' | 'view_messages' | 'settings';
  label: string;
  data?: any;
}

// LINE message filter
export interface LineMessageFilter {
  userId?: string;
  messageType?: string;
  dateFrom?: Date;
  dateTo?: Date;
}
