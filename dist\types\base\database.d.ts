import { BaseFilter, PaginationOptions, TimestampFields } from './common';
export interface DatabaseModel extends TimestampFields {
    id: string;
}
export interface DatabaseOperations<T, CreateData, UpdateData, FilterType = BaseFilter> {
    create(data: CreateData): Promise<T>;
    findById(id: string): Promise<T | null>;
    findMany(filter?: FilterType, pagination?: PaginationOptions): Promise<{
        items: T[];
        total: number;
    }>;
    update(id: string, data: UpdateData): Promise<void>;
    delete(id: string): Promise<void>;
}
export interface DatabaseConnection {
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    run(sql: string, params?: any[]): Promise<any>;
    get<T>(sql: string, params?: any[]): Promise<T | undefined>;
    all<T>(sql: string, params?: any[]): Promise<T[]>;
}
export interface LogEntry {
    id: string;
    type: 'sms' | 'line' | 'system';
    payload: string;
    processed: boolean;
    createdAt: Date;
    processedAt?: Date;
    error?: string;
}
export interface CreateLogEntryData {
    type: LogEntry['type'];
    payload: string;
    processed?: boolean;
    error?: string;
}
export interface CreateWebhookLogData {
    type: 'sms' | 'line';
    payload: any;
}
export interface UpdateWebhookLogData {
    processed?: boolean | undefined;
    error?: string | undefined;
}
export interface WebhookLogFilter {
    type?: 'sms' | 'line' | undefined;
    processed?: boolean | undefined;
    dateFrom?: Date | undefined;
    dateTo?: Date | undefined;
}
//# sourceMappingURL=database.d.ts.map