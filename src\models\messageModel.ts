import { v4 as uuidv4 } from 'uuid';
import database from './database';
import { StoredMessage, ProcessState, RecipientState } from '../types';
import logger from '../utils/logger';

export interface CreateMessageData {
  phoneNumbers: string[];
  message: string;
  lineUserId?: string | undefined;
  ttl?: number | undefined;
  simNumber?: number | undefined;
  withDeliveryReport?: boolean | undefined;
}

export interface UpdateMessageData {
  status?: ProcessState;
  deliveredAt?: Date;
  failedReason?: string;
}

export interface MessageFilter {
  status?: ProcessState;
  phoneNumber?: string;
  lineUserId?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

class MessageModel {
  async create(data: CreateMessageData): Promise<StoredMessage> {
    const id = uuidv4();
    const now = new Date();

    const message: StoredMessage = {
      id,
      phoneNumbers: JSON.stringify(data.phoneNumbers),
      message: data.message,
      status: ProcessState.Pending,
      createdAt: now,
      updatedAt: now,
      lineUserId: data.lineUserId,
    };

    await database.run(
      `INSERT INTO messages (
        id, phone_numbers, message, status, created_at, updated_at, 
        line_user_id, ttl, sim_number, with_delivery_report
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        message.id,
        message.phoneNumbers,
        message.message,
        message.status,
        message.createdAt.toISOString(),
        message.updatedAt.toISOString(),
        message.lineUserId || null,
        data.ttl || null,
        data.simNumber || null,
        data.withDeliveryReport ? 1 : 0,
      ]
    );

    // Create recipient records
    for (const phoneNumber of data.phoneNumbers) {
      await database.run(
        `INSERT INTO message_recipients (
          id, message_id, phone_number, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          uuidv4(),
          id,
          phoneNumber,
          ProcessState.Pending,
          now.toISOString(),
          now.toISOString(),
        ]
      );
    }

    logger.info('Message created in database', {
      messageId: id,
      phoneNumbers: data.phoneNumbers,
      messageLength: data.message.length,
    });

    return message;
  }

  async findById(id: string): Promise<StoredMessage | null> {
    const row = await database.get<any>(
      'SELECT * FROM messages WHERE id = ?',
      [id]
    );

    if (!row) {
      return null;
    }

    return this.mapRowToMessage(row);
  }

  async update(id: string, data: UpdateMessageData): Promise<void> {
    const updates: string[] = [];
    const values: any[] = [];

    if (data.status !== undefined) {
      updates.push('status = ?');
      values.push(data.status);
    }

    if (data.deliveredAt !== undefined) {
      updates.push('delivered_at = ?');
      values.push(data.deliveredAt.toISOString());
    }

    if (data.failedReason !== undefined) {
      updates.push('failed_reason = ?');
      values.push(data.failedReason);
    }

    updates.push('updated_at = ?');
    values.push(new Date().toISOString());

    values.push(id);

    await database.run(
      `UPDATE messages SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    logger.debug('Message updated in database', { messageId: id, updates: data });
  }

  async updateRecipientStatus(
    messageId: string,
    phoneNumber: string,
    status: ProcessState,
    error?: string | null
  ): Promise<void> {
    const updates = ['status = ?', 'updated_at = ?'];
    const values = [status, new Date().toISOString()];

    if (status === ProcessState.Delivered) {
      updates.push('delivered_at = ?');
      values.push(new Date().toISOString());
    }

    if (error) {
      updates.push('error = ?');
      values.push(error);
    }

    values.push(messageId, phoneNumber);

    await database.run(
      `UPDATE message_recipients SET ${updates.join(', ')} 
       WHERE message_id = ? AND phone_number = ?`,
      values
    );

    logger.debug('Recipient status updated', {
      messageId,
      phoneNumber,
      status,
      error,
    });
  }

  async getRecipients(messageId: string): Promise<RecipientState[]> {
    const rows = await database.all<any>(
      'SELECT * FROM message_recipients WHERE message_id = ?',
      [messageId]
    );

    return rows.map(row => ({
      phoneNumber: row.phone_number,
      state: row.status as ProcessState,
      error: row.error !== undefined ? row.error : null,
    }));
  }

  async findMany(
    filter: MessageFilter = {},
    pagination: PaginationOptions = { page: 1, limit: 20 }
  ): Promise<{ messages: StoredMessage[]; total: number }> {
    const conditions: string[] = [];
    const values: any[] = [];

    if (filter.status) {
      conditions.push('status = ?');
      values.push(filter.status);
    }

    if (filter.lineUserId) {
      conditions.push('line_user_id = ?');
      values.push(filter.lineUserId);
    }

    if (filter.dateFrom) {
      conditions.push('created_at >= ?');
      values.push(filter.dateFrom.toISOString());
    }

    if (filter.dateTo) {
      conditions.push('created_at <= ?');
      values.push(filter.dateTo.toISOString());
    }

    if (filter.phoneNumber) {
      conditions.push('phone_numbers LIKE ?');
      values.push(`%${filter.phoneNumber}%`);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countResult = await database.get<{ count: number }>(
      `SELECT COUNT(*) as count FROM messages ${whereClause}`,
      values
    );
    const total = countResult?.count || 0;

    // Get paginated results
    const offset = (pagination.page - 1) * pagination.limit;
    const rows = await database.all<any>(
      `SELECT * FROM messages ${whereClause} 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [...values, pagination.limit, offset]
    );

    const messages = rows.map(row => this.mapRowToMessage(row));

    return { messages, total };
  }

  async deleteOldMessages(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await database.run(
      'DELETE FROM messages WHERE created_at < ?',
      [cutoffDate.toISOString()]
    );

    logger.info('Old messages deleted', {
      deletedCount: result.changes,
      cutoffDate: cutoffDate.toISOString(),
    });

    return result.changes || 0;
  }

  private mapRowToMessage(row: any): StoredMessage {
    return {
      id: row.id,
      phoneNumbers: row.phone_numbers,
      message: row.message,
      status: row.status as ProcessState,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      deliveredAt: row.delivered_at ? new Date(row.delivered_at) : undefined,
      failedReason: row.failed_reason || undefined,
      lineUserId: row.line_user_id || undefined,
    };
  }
}

export default new MessageModel();
