import database from './database';
import { LineUser } from '../types';
import logger from '../utils/logger';

export interface CreateLineUserData {
  id: string;
  displayName?: string | undefined;
  pictureUrl?: string | undefined;
  statusMessage?: string | undefined;
  isAdmin?: boolean | undefined;
}

export interface UpdateLineUserData {
  displayName?: string;
  pictureUrl?: string;
  statusMessage?: string;
  isAdmin?: boolean;
}

class LineUserModel {
  async create(data: CreateLineUserData): Promise<LineUser> {
    const now = new Date();

    const user: LineUser = {
      id: data.id,
      displayName: data.displayName,
      pictureUrl: data.pictureUrl,
      statusMessage: data.statusMessage,
      isAdmin: data.isAdmin || false,
      createdAt: now,
      updatedAt: now,
    };

    await database.run(
      `INSERT INTO line_users (
        id, display_name, picture_url, status_message, is_admin, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        user.id,
        user.displayName || null,
        user.pictureUrl || null,
        user.statusMessage || null,
        user.isAdmin ? 1 : 0,
        user.createdAt.toISOString(),
        user.updatedAt.toISOString(),
      ]
    );

    logger.info('LINE user created in database', {
      userId: user.id,
      displayName: user.displayName,
      isAdmin: user.isAdmin,
    });

    return user;
  }

  async findById(id: string): Promise<LineUser | null> {
    const row = await database.get<any>(
      'SELECT * FROM line_users WHERE id = ?',
      [id]
    );

    if (!row) {
      return null;
    }

    return this.mapRowToUser(row);
  }

  async update(id: string, data: UpdateLineUserData): Promise<void> {
    const updates: string[] = [];
    const values: any[] = [];

    if (data.displayName !== undefined) {
      updates.push('display_name = ?');
      values.push(data.displayName);
    }

    if (data.pictureUrl !== undefined) {
      updates.push('picture_url = ?');
      values.push(data.pictureUrl);
    }

    if (data.statusMessage !== undefined) {
      updates.push('status_message = ?');
      values.push(data.statusMessage);
    }

    if (data.isAdmin !== undefined) {
      updates.push('is_admin = ?');
      values.push(data.isAdmin ? 1 : 0);
    }

    updates.push('updated_at = ?');
    values.push(new Date().toISOString());

    values.push(id);

    await database.run(
      `UPDATE line_users SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    logger.debug('LINE user updated in database', { userId: id, updates: data });
  }

  async upsert(data: CreateLineUserData): Promise<LineUser> {
    const existingUser = await this.findById(data.id);

    if (existingUser) {
      await this.update(data.id, {
        displayName: data.displayName,
        pictureUrl: data.pictureUrl,
        statusMessage: data.statusMessage,
        isAdmin: data.isAdmin,
      });

      return await this.findById(data.id) as LineUser;
    } else {
      return await this.create(data);
    }
  }

  async findAll(): Promise<LineUser[]> {
    const rows = await database.all<any>(
      'SELECT * FROM line_users ORDER BY created_at DESC'
    );

    return rows.map(row => this.mapRowToUser(row));
  }

  async findAdmins(): Promise<LineUser[]> {
    const rows = await database.all<any>(
      'SELECT * FROM line_users WHERE is_admin = 1 ORDER BY created_at DESC'
    );

    return rows.map(row => this.mapRowToUser(row));
  }

  async setAdmin(id: string, isAdmin: boolean): Promise<void> {
    await this.update(id, { isAdmin });

    logger.info('LINE user admin status updated', { userId: id, isAdmin });
  }

  async delete(id: string): Promise<void> {
    await database.run('DELETE FROM line_users WHERE id = ?', [id]);

    logger.info('LINE user deleted from database', { userId: id });
  }

  async getStats(): Promise<{
    totalUsers: number;
    adminUsers: number;
    recentUsers: number;
  }> {
    const totalResult = await database.get<{ count: number }>(
      'SELECT COUNT(*) as count FROM line_users'
    );

    const adminResult = await database.get<{ count: number }>(
      'SELECT COUNT(*) as count FROM line_users WHERE is_admin = 1'
    );

    const recentDate = new Date();
    recentDate.setDate(recentDate.getDate() - 7);

    const recentResult = await database.get<{ count: number }>(
      'SELECT COUNT(*) as count FROM line_users WHERE created_at >= ?',
      [recentDate.toISOString()]
    );

    return {
      totalUsers: totalResult?.count || 0,
      adminUsers: adminResult?.count || 0,
      recentUsers: recentResult?.count || 0,
    };
  }

  private mapRowToUser(row: any): LineUser {
    return {
      id: row.id,
      displayName: row.display_name || undefined,
      pictureUrl: row.picture_url || undefined,
      statusMessage: row.status_message || undefined,
      isAdmin: row.is_admin === 1,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}

export default new LineUserModel();
