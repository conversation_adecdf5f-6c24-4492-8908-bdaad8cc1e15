import { HttpClient } from '../types';
import logger from '../utils/logger';

class FetchHttpClient implements HttpClient {
  private baseHeaders: Record<string, string>;

  constructor() {
    this.baseHeaders = {
      'Content-Type': 'application/json',
      'User-Agent': 'SMS-Gateway-API/1.0.0',
    };
  }

  private async makeRequest<T>(
    url: string,
    options: RequestInit,
    headers?: Record<string, string>
  ): Promise<T> {
    const requestHeaders = {
      ...this.baseHeaders,
      ...headers,
    };

    const requestOptions: RequestInit = {
      ...options,
      headers: requestHeaders,
    };

    logger.debug(`HTTP ${options.method || 'GET'} ${url}`, {
      headers: requestHeaders,
      body: options.body,
    });

    try {
      const response = await fetch(url, requestOptions);

      if (!response.ok) {
        const errorText = await response.text();
        logger.error(`HTTP ${response.status} ${response.statusText}`, {
          url,
          method: options.method,
          status: response.status,
          statusText: response.statusText,
          body: errorText,
        });

        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const contentType = response.headers.get('content-type');
      let data: T;

      if (contentType && contentType.includes('application/json')) {
        data = await response.json() as T;
      } else {
        data = await response.text() as unknown as T;
      }

      logger.debug(`HTTP ${options.method || 'GET'} ${url} - Success`, {
        status: response.status,
        data: typeof data === 'string' ? data.substring(0, 200) : data,
      });

      return data;
    } catch (error) {
      logger.error(`HTTP request failed: ${url}`, {
        method: options.method,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  async get<T>(url: string, headers?: Record<string, string>): Promise<T> {
    return this.makeRequest<T>(url, { method: 'GET' }, headers);
  }

  async post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T> {
    return this.makeRequest<T>(
      url,
      {
        method: 'POST',
        body: JSON.stringify(body),
      },
      headers
    );
  }

  async put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T> {
    return this.makeRequest<T>(
      url,
      {
        method: 'PUT',
        body: JSON.stringify(body),
      },
      headers
    );
  }

  async patch<T>(url: string, body: any, headers?: Record<string, string>): Promise<T> {
    return this.makeRequest<T>(
      url,
      {
        method: 'PATCH',
        body: JSON.stringify(body),
      },
      headers
    );
  }

  async delete<T>(url: string, headers?: Record<string, string>): Promise<T> {
    return this.makeRequest<T>(url, { method: 'DELETE' }, headers);
  }
}

export default FetchHttpClient;
