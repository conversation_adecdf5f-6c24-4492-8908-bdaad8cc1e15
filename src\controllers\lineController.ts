import { Request, Response } from 'express';
import { WebhookEvent } from '@line/bot-sdk';
import lineService from '../services/lineService';
import webhookLogModel from '../models/webhookLogModel';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../utils/logger';

class LineController {
  /**
   * Handle LINE webhook
   * POST /webhook/line
   */
  handleWebhook = asyncHandler(async (req: Request, res: Response) => {
    const events: WebhookEvent[] = req.body.events || [];

    logger.info('LINE webhook received', {
      eventsCount: events.length,
      events: events.map(e => ({ type: e.type, source: e.source })),
    });

    // Log webhook for debugging
    await webhookLogModel.create({
      type: 'line',
      payload: req.body,
    });

    if (events.length === 0) {
      res.status(200).json({ success: true, message: 'No events to process' });
      return;
    }

    try {
      // Process webhook events
      await lineService.handleWebhookEvents(events);

      res.status(200).json({
        success: true,
        message: 'Webhook processed successfully',
        processedEvents: events.length,
      });
    } catch (error) {
      logger.error('Failed to process LINE webhook', {
        error: (error as Error).message,
        eventsCount: events.length,
      });

      // Still return 200 to prevent LINE from retrying
      res.status(200).json({
        success: false,
        error: 'Failed to process webhook',
        processedEvents: 0,
      });
    }
  });

  /**
   * Send message to user
   * POST /api/line/send
   */
  sendMessage = asyncHandler(async (req: Request, res: Response) => {
    const { userId, message } = req.body;

    if (!userId || !message) {
      return res.status(400).json({
        success: false,
        error: 'userId and message are required',
      });
    }

    logger.info('Send LINE message request', { userId, messageType: typeof message });

    await lineService.pushMessage(userId, message);

    res.json({
      success: true,
      message: 'Message sent successfully',
    });
  });

  /**
   * Broadcast message to all users
   * POST /api/line/broadcast
   */
  broadcastMessage = asyncHandler(async (req: Request, res: Response) => {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        error: 'message is required',
      });
    }

    logger.info('Broadcast LINE message request', { messageType: typeof message });

    await lineService.broadcastMessage(message);

    res.json({
      success: true,
      message: 'Broadcast message sent successfully',
    });
  });

  /**
   * Send message to admin users
   * POST /api/line/send-to-admins
   */
  sendToAdmins = asyncHandler(async (req: Request, res: Response) => {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        error: 'message is required',
      });
    }

    logger.info('Send to admins request', { messageType: typeof message });

    await lineService.sendToAdmins(message);

    res.json({
      success: true,
      message: 'Message sent to admins successfully',
    });
  });

  /**
   * Get LINE Bot info
   * GET /api/line/info
   */
  getBotInfo = asyncHandler(async (req: Request, res: Response) => {
    logger.debug('Get LINE Bot info request');

    // This would typically get bot profile info from LINE API
    res.json({
      success: true,
      data: {
        status: 'active',
        webhookUrl: `${req.protocol}://${req.get('host')}/webhook/line`,
        features: {
          quickReply: true,
          richMenu: false,
          broadcast: true,
        },
      },
    });
  });

  /**
   * Health check for LINE Bot service
   * GET /api/line/health
   */
  healthCheck = asyncHandler(async (req: Request, res: Response) => {
    logger.debug('LINE Bot health check request');

    res.json({
      success: true,
      data: {
        lineBot: 'active',
        webhook: 'configured',
        timestamp: new Date().toISOString(),
      },
      message: 'LINE Bot service is healthy',
    });
  });
}

export default new LineController();
