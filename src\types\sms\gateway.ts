/**
 * SMS Gateway client types and interfaces
 */

import { HttpClient, HealthResponse } from '../base/api';
import { SMSMessage, MessageState } from './message';
import { WebHook, RegisterWebHookRequest } from './webhook';

// SMS Gateway client interface
export interface SMSGatewayClient {
  // Message operations
  sendMessage(message: SMSMessage, options?: SendOptions): Promise<MessageState>;
  getMessageState(messageId: string): Promise<MessageState>;
  
  // Webhook operations
  getWebhooks(): Promise<WebHook[]>;
  registerWebhook(request: RegisterWebHookRequest): Promise<WebHook>;
  deleteWebhook(webhookId: string): Promise<void>;
  
  // Device operations
  getDevices(): Promise<Device[]>;
  deleteDevice(deviceId: string): Promise<void>;
  
  // Settings operations
  getSettings(): Promise<DeviceSettings>;
  updateSettings(settings: DeviceSettings): Promise<void>;
  patchSettings(settings: Partial<DeviceSettings>): Promise<void>;
  
  // Health and logs
  getHealth(): Promise<HealthResponse>;
  exportInbox(request: MessagesExportRequest): Promise<void>;
  getLogs(from?: Date, to?: Date): Promise<GatewayLogEntry[]>;
  
  // Connection management
  isHealthy(): Promise<boolean>;
}

// Send options
export interface SendOptions {
  skipPhoneValidation?: boolean;
}

// Device interface
export interface Device {
  id: string;
  name: string;
  createdAt: string;
  lastSeen: string;
  updatedAt: string;
  deletedAt?: string | null;
}

// Messages export request
export interface MessagesExportRequest {
  deviceId: string;
  since: Date;
  until: Date;
}

// Gateway log entry (different from database LogEntry)
export interface GatewayLogEntry {
  id: number;
  createdAt: string;
  module: string;
  priority: LogEntryPriority;
  message: string;
  context?: Record<string, string>;
}

export enum LogEntryPriority {
  Debug = 'DEBUG',
  Info = 'INFO',
  Warn = 'WARN',
  Error = 'ERROR'
}

// Health response is imported above and used in interface

// Device settings (will be defined in device/settings.ts)
export interface DeviceSettings {
  messages?: any;
  webhooks?: any;
  gateway?: any;
  encryption?: any;
  logs?: any;
  ping?: any;
}
