import { WebHook } from '../types';
interface SMSWebhookPayload {
    event: string;
    messageId?: string;
    deviceId: string;
    phoneNumber?: string;
    message?: string;
    status?: string;
    timestamp: string;
    error?: string;
}
declare class WebhookService {
    registerSMSWebhook(): Promise<WebHook>;
    getWebhooks(): Promise<WebHook[]>;
    deleteWebhook(webhookId: string): Promise<void>;
    handleSMSWebhook(payload: SMSWebhookPayload): Promise<void>;
    private handleSMSReceived;
    private handleSMSSent;
    private handleSMSDelivered;
    private handleSMSFailed;
    setupWebhooks(): Promise<void>;
}
declare const _default: WebhookService;
export default _default;
//# sourceMappingURL=webhookService.d.ts.map