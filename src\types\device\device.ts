/**
 * Device types and interfaces
 */

import { BaseEntity } from '../base/common';

// Device entity
export interface Device extends BaseEntity {
  name: string;
  lastSeen: string;
  deletedAt?: string | null;
}

// Device creation data
export interface CreateDeviceData {
  name: string;
  lastSeen?: string;
}

// Device update data
export interface UpdateDeviceData {
  name?: string;
  lastSeen?: string;
  deletedAt?: string | null;
}

// Device filter
export interface DeviceFilter {
  name?: string;
  active?: boolean; // Filter for non-deleted devices
  lastSeenAfter?: Date;
  lastSeenBefore?: Date;
}
