import { Device, DeviceSettings } from '../types';
declare class DeviceService {
    getDevices(): Promise<Device[]>;
    getDevice(deviceId: string): Promise<Device>;
    deleteDevice(deviceId: string): Promise<void>;
    getSettings(): Promise<DeviceSettings>;
    updateSettings(settings: DeviceSettings): Promise<void>;
    patchSettings(settings: Partial<DeviceSettings>): Promise<void>;
    getHealth(): Promise<any>;
    getLogs(from?: Date, to?: Date): Promise<any[]>;
    exportInbox(deviceId: string, since: Date, until: Date): Promise<void>;
    getDeviceStats(): Promise<{
        totalDevices: number;
        activeDevices: number;
        recentlySeenDevices: number;
        devicesByStatus: Array<{
            status: string;
            count: number;
        }>;
    }>;
    testConnection(): Promise<boolean>;
}
declare const _default: DeviceService;
export default _default;
//# sourceMappingURL=deviceService.d.ts.map