import App from './app';
import config, { validateConfig } from './config';
import logger from './utils/logger';
import database from './models/database';
import webhookService from './services/webhookService';

// Validate configuration
try {
  validateConfig();
  logger.info('Configuration validation passed');
} catch (error) {
  logger.error('Configuration validation failed', { error: (error as Error).message });
  process.exit(1);
}

// Handle uncaught exceptions
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Rejection', { reason, promise });
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Initialize application
async function initializeApp(): Promise<App> {
  try {
    // Initialize database
    logger.info('Initializing database...');
    await database.connect();
    await database.initializeTables();
    await database.migrate();
    logger.info('Database initialized successfully');

    // Start the application
    const app = new App();
    app.listen();

    // Setup webhooks after server starts
    setTimeout(async () => {
      try {
        await webhookService.setupWebhooks();
      } catch (error) {
        logger.error('Failed to setup webhooks', { error: (error as Error).message });
      }
    }, 5000); // Wait 5 seconds for server to be ready

    logger.info('SMS Gateway API started successfully', {
      nodeEnv: config.server.nodeEnv,
      port: config.server.port,
    });

    return app;
  } catch (error) {
    logger.error('Failed to initialize application', { error: (error as Error).message });
    process.exit(1);
  }
}

// Start the application
initializeApp().catch(error => {
  logger.error('Failed to start application', { error: error.message });
  process.exit(1);
});
